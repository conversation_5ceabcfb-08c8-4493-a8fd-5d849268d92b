import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class NotificationDetailScreen extends StatefulWidget {
  final Map<String, dynamic> notification;
  final bool isDarkMode;

  const NotificationDetailScreen({
    super.key,
    required this.notification,
    this.isDarkMode = false,
  });

  @override
  State<NotificationDetailScreen> createState() => _NotificationDetailScreenState();
}

class _NotificationDetailScreenState extends State<NotificationDetailScreen> {
  @override
  Widget build(BuildContext context) {
    final notification = widget.notification;
    final isLockerNotification = notification['type'] == 'locker_rental' || 
                                notification['type'] == 'locker_confirmed' ||
                                notification['type'] == 'product_placed';
    
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Notification Details',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              Icons.delete_outline,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
            onPressed: () {
              _showDeleteConfirmation();
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Notification Header
            _buildNotificationHeader(),
            const SizedBox(height: 24),
            
            // Notification Content
            _buildNotificationContent(),
            
            // QR Code and PIN (if applicable)
            if (isLockerNotification) ...[
              const SizedBox(height: 32),
              _buildAccessInformation(),
            ],
            
            const SizedBox(height: 32),
            
            // Action Buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationHeader() {
    final notification = widget.notification;
    final IconData notificationIcon = _getNotificationIcon(notification['type']);
    final Color iconColor = _getNotificationColor(notification['type']);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: widget.isDarkMode 
              ? Colors.white.withOpacity(0.1) 
              : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  notificationIcon,
                  color: iconColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      notification['title'],
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification['time'] ?? 'Just now',
                      style: TextStyle(
                        fontSize: 14,
                        color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationContent() {
    final notification = widget.notification;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: widget.isDarkMode 
              ? Colors.white.withOpacity(0.1) 
              : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Message',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
          ),
          const SizedBox(height: 12),
          Text(
            notification['message'],
            style: TextStyle(
              fontSize: 16,
              height: 1.5,
              color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAccessInformation() {
    final notification = widget.notification;
    final qrCode = notification['qrCode'] ?? _generateQRCode();
    final accessPin = notification['accessPin'] ?? _generateAccessPin();
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: widget.isDarkMode 
              ? Colors.white.withOpacity(0.1) 
              : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Access Information',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
          ),
          const SizedBox(height: 20),
          
          // QR Code Section
          Center(
            child: Column(
              children: [
                Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade300, width: 2),
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.qr_code,
                      size: 120,
                      color: Colors.black,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'QR Code for Locker Access',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: widget.isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
                const SizedBox(height: 8),
                GestureDetector(
                  onTap: () => _copyToClipboard(qrCode, 'QR Code'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: widget.isDarkMode ? Colors.grey.shade700 : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          qrCode,
                          style: TextStyle(
                            fontSize: 12,
                            fontFamily: 'monospace',
                            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.copy,
                          size: 16,
                          color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Access PIN Section
          _buildInfoRow('Access PIN', accessPin, canCopy: true),
          const SizedBox(height: 12),
          _buildInfoRow('Expires', '15 minutes after generation'),
          
          const SizedBox(height: 20),
          
          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: widget.isDarkMode ? Colors.grey.shade700 : const Color(0xFFFEF3E2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Instructions:',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '1. Go to the specified hublocker location\n'
                  '2. Scan the QR code or enter the PIN on the locker keypad\n'
                  '3. The locker will open automatically\n'
                  '4. Place or retrieve your item\n'
                  '5. Close the locker securely',
                  style: TextStyle(
                    fontSize: 12,
                    height: 1.4,
                    color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool canCopy = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
          ),
        ),
        GestureDetector(
          onTap: canCopy ? () => _copyToClipboard(value, label) : null,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: widget.isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              if (canCopy) ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.copy,
                  size: 16,
                  color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final notification = widget.notification;
    final isLockerNotification = notification['type'] == 'locker_rental' ||
                                notification['type'] == 'locker_confirmed' ||
                                notification['type'] == 'product_placed';

    return Column(
      children: [
        if (isLockerNotification) ...[
          // Primary Action Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                _handlePrimaryAction();
              },
              icon: const Icon(Icons.location_on, color: Colors.white),
              label: const Text(
                'View Hublocker Location',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF708871),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],

        // Mark as Read Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              _markAsRead();
            },
            icon: Icon(
              Icons.check,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
            label: Text(
              'Mark as Read',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'locker_rental':
      case 'locker_confirmed':
        return Icons.lock_open;
      case 'product_placed':
        return Icons.inventory;
      case 'due_date':
        return Icons.schedule;
      case 'delivery':
        return Icons.local_shipping;
      case 'payment':
        return Icons.payment;
      case 'review':
        return Icons.star;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'locker_rental':
      case 'locker_confirmed':
        return const Color(0xFF708871);
      case 'product_placed':
        return Colors.blue;
      case 'due_date':
        return Colors.red;
      case 'delivery':
        return Colors.orange;
      case 'payment':
        return Colors.purple;
      case 'review':
        return Colors.amber;
      default:
        return const Color(0xFF708871);
    }
  }

  String _generateQRCode() {
    return 'PINGGO_${DateTime.now().millisecondsSinceEpoch}_${(DateTime.now().millisecondsSinceEpoch % 10000)}';
  }

  String _generateAccessPin() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return (random % 900000 + 100000).toString();
  }

  void _copyToClipboard(String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$label copied to clipboard'),
        backgroundColor: const Color(0xFF708871),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handlePrimaryAction() {
    // Navigate to hublocker location or show map
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening hublocker location...'),
        backgroundColor: Color(0xFF708871),
      ),
    );
  }

  void _markAsRead() {
    Navigator.pop(context, true); // Return true to indicate marked as read
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        title: Text(
          'Delete Notification',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this notification?',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context, 'delete'); // Return delete action
            },
            child: const Text(
              'Delete',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
