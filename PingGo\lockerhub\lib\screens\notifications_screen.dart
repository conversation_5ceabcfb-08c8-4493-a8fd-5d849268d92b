import 'package:flutter/material.dart';
import 'package:lockerhub/screens/notification_detail_screen.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  final List<Map<String, dynamic>> notifications = [
    {
      'id': '1',
      'type': 'locker_confirmed',
      'title': 'Locker Rental Confirmed!',
      'message': 'Your locker rental has been confirmed! Use the QR code or PIN to access your locker at HUST Hublocker.',
      'time': '30 minutes ago',
      'isRead': false,
      'icon': Icons.lock_open,
      'color': const Color(0xFF708871),
      'qrCode': 'PINGGO_1234567890_5678',
      'accessPin': '123456',
    },
    {
      'id': '2',
      'type': 'product_placed',
      'title': 'Product Ready for Pickup',
      'message': 'Your "Modern Lemon Chair" has been placed in the locker. Use the QR code below to retrieve it.',
      'time': '1 hour ago',
      'isRead': false,
      'icon': Icons.inventory,
      'color': Colors.blue,
      'qrCode': 'PINGGO_0987654321_1234',
      'accessPin': '789012',
    },
    {
      'id': '3',
      'type': 'due_date',
      'title': 'Rental Due Tomorrow',
      'message': 'Your rental "Electric Plug" is due tomorrow! Please return it to HUST Hublocker.',
      'time': '2 hours ago',
      'isRead': false,
      'icon': Icons.schedule,
      'color': Colors.red,
    },
    {
      'id': '4',
      'type': 'delivery',
      'title': 'Delivery Complete',
      'message': 'Your item "Calculus Book" has been delivered to Logic Box Golden View and is now available for rent.',
      'time': '3 hours ago',
      'isRead': false,
      'icon': Icons.local_shipping,
      'color': const Color(0xFF708871),
    },
    {
      'id': '3',
      'type': 'order_confirmed',
      'title': 'Order Confirmed',
      'message': 'Your rental request for Modern Black Chair has been confirmed by the owner.',
      'time': '5 hours ago',
      'isRead': false,
      'icon': Icons.check_circle,
      'color': Colors.green,
    },
    {
      'id': '4',
      'type': 'item_ready',
      'title': 'Item Ready for Pickup',
      'message': 'Your Modern Lemon Chair is now available at HUST Hublocker. Pickup code: #HU2024',
      'time': '8 hours ago',
      'isRead': true,
      'icon': Icons.inventory_2,
      'color': const Color(0xFF708871),
    },
    {
      'id': '5',
      'type': 'status_change',
      'title': 'Status Update',
      'message': 'Your item "HUST Uniform" is now being rented by Nguyen Minh Tuan.',
      'time': '1 day ago',
      'isRead': true,
      'icon': Icons.update,
      'color': Colors.blue,
    },
    {
      'id': '6',
      'type': 'payment',
      'title': 'Payment Successful',
      'message': 'Payment of 50,000 VND for your monthly rental has been processed successfully.',
      'time': '2 days ago',
      'isRead': true,
      'icon': Icons.payment,
      'color': Colors.blue,
    },
    {
      'id': '7',
      'type': 'cancellation',
      'title': 'Rental Cancelled',
      'message': 'Your lease for "Gaming Chair" has been cancelled. Refund of 40,000 VND will be processed within 3-5 business days.',
      'time': '3 days ago',
      'isRead': true,
      'icon': Icons.cancel_outlined,
      'color': Colors.orange,
    },
    {
      'id': '8',
      'type': 'new_item',
      'title': 'New Items Available',
      'message': 'Check out the latest furniture additions at FPT Hublocker near you!',
      'time': '4 days ago',
      'isRead': true,
      'icon': Icons.new_releases,
      'color': Colors.purple,
    },
  ];

  @override
  Widget build(BuildContext context) {
    final unreadCount = notifications.where((n) => !n['isRead']).length;

    return Scaffold(
      backgroundColor: const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF708871)),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notifications',
              style: TextStyle(
                color: Color(0xFF708871),
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            if (unreadCount > 0)
              Text(
                '$unreadCount new notifications',
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
          ],
        ),
        actions: [
          if (unreadCount > 0)
            TextButton(
              onPressed: () {
                setState(() {
                  for (var notification in notifications) {
                    notification['isRead'] = true;
                  }
                });
              },
              child: const Text(
                'Mark all read',
                style: TextStyle(
                  color: Color(0xFF708871),
                  fontSize: 14,
                ),
              ),
            ),
        ],
      ),
      body: notifications.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                return _buildNotificationCard(notification, index);
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No notifications yet',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You\'ll see updates about your orders and rentals here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(Map<String, dynamic> notification, int index) {
    final isRead = notification['isRead'] as bool;

    return Dismissible(
      key: Key(notification['id']),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: Colors.red,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
          size: 24,
        ),
      ),
      onDismissed: (direction) {
        setState(() {
          notifications.removeAt(index);
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification deleted'),
            backgroundColor: Colors.red,
          ),
        );
      },
      child: GestureDetector(
        onTap: () async {
          // Navigate to notification detail screen
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => NotificationDetailScreen(
                notification: notification,
                isDarkMode: false,
              ),
            ),
          );

          // Handle result from detail screen
          if (result == true) {
            // Mark as read
            setState(() {
              notification['isRead'] = true;
            });
          } else if (result == 'delete') {
            // Delete notification
            setState(() {
              notifications.removeAt(index);
            });
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Notification deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
        child: Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isRead ? Colors.white : const Color(0xFF708871).withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isRead ? Colors.grey.shade200 : const Color(0xFF708871).withValues(alpha: 0.2),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: notification['color'].withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  notification['icon'],
                  color: notification['color'],
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification['title'],
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: isRead ? FontWeight.w500 : FontWeight.bold,
                              color: const Color(0xFF708871),
                            ),
                          ),
                        ),
                        if (!isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Color(0xFF708871),
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification['message'],
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      notification['time'],
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
