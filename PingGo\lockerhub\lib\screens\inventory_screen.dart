import 'package:flutter/material.dart';
import 'package:lockerhub/models/inventory_item.dart';
import 'package:lockerhub/screens/inventory_product_detail_screen.dart';
import 'package:lockerhub/services/inventory_service.dart';
import 'package:lockerhub/screens/lease_product_management_screen.dart';

class InventoryScreen extends StatefulWidget {
  final bool isDarkMode;

  const InventoryScreen({super.key, this.isDarkMode = false});

  @override
  State<InventoryScreen> createState() => _InventoryScreenState();
}

class _InventoryScreenState extends State<InventoryScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final InventoryService _inventoryService = InventoryService();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Get inventory data from service
  List<InventoryItem> get inventoryItems => _inventoryService.allItems;

  List<InventoryItem> getItemsByStatus(InventoryStatus status) {
    return _inventoryService.getItemsByStatus(status);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Inventory',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
          unselectedLabelColor: widget.isDarkMode ? Colors.grey : Colors.grey.shade600,
          indicatorColor: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
          tabs: const [
            Tab(text: 'Rented Product'),
            Tab(text: 'Lease Product'),
            Tab(text: 'Delivery Product'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildInventoryList(InventoryStatus.rented),
          _buildInventoryList(InventoryStatus.lease),
          _buildInventoryList(InventoryStatus.delivery),
        ],
      ),
    );
  }

  Widget _buildInventoryList(InventoryStatus status) {
    final items = getItemsByStatus(status);
    
    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: widget.isDarkMode ? Colors.grey : Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'No ${status.name} items',
              style: TextStyle(
                fontSize: 18,
                color: widget.isDarkMode ? Colors.grey : Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your ${status.name} products will appear here',
              style: TextStyle(
                fontSize: 14,
                color: widget.isDarkMode ? Colors.grey : Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        return _buildInventoryCard(items[index]);
      },
    );
  }

  Widget _buildInventoryCard(InventoryItem item) {
    return GestureDetector(
      onTap: () {
        if (item.status == InventoryStatus.lease) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => LeaseProductManagementScreen(
                item: item,
                isDarkMode: widget.isDarkMode,
              ),
            ),
          );
        } else {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => InventoryProductDetailScreen(
                item: item,
                isDarkMode: widget.isDarkMode,
              ),
            ),
          );
        }
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: widget.isDarkMode 
                ? Colors.white.withValues(alpha: 0.1) 
                : Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Product Image
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFF708871).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.inventory,
                color: Color(0xFF708871),
                size: 40,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Product Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: widget.isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  
                  // Owner/Renter info
                  if (item.status == InventoryStatus.rented && item.ownerName != null)
                    Text(
                      'Owner: ${item.ownerName}',
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                      ),
                    ),
                  if (item.status == InventoryStatus.delivery && item.renterName != null)
                    Text(
                      'Renter: ${item.renterName}',
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                      ),
                    ),
                  
                  const SizedBox(height: 4),
                  
                  // Status and Due Date
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getStatusColor(item.status),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          item.statusDisplayText,
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Show location for lease items
                      if (item.status == InventoryStatus.lease)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _getLocationColor(item.productLocation),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            item.productLocationDisplayText,
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      if (item.dueDate != null && item.status != InventoryStatus.lease)
                        Text(
                          'Due: ${_formatDate(item.dueDate!)}',
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Arrow Icon
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(InventoryStatus status) {
    switch (status) {
      case InventoryStatus.rented:
        return Colors.red.shade400;
      case InventoryStatus.lease:
        return Colors.teal.shade400;
      case InventoryStatus.delivery:
        return Colors.orange.shade400;
    }
  }

  Color _getLocationColor(ProductLocation location) {
    switch (location) {
      case ProductLocation.inLocker:
        return Colors.teal.shade400;
      case ProductLocation.notDelivered:
        return Colors.red.shade400;
      case ProductLocation.inTransit:
        return Colors.orange.shade400;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
