import 'package:flutter/foundation.dart';
import 'package:lockerhub/models/inventory_item.dart';
import 'package:lockerhub/services/notification_service.dart';

class InventoryService extends ChangeNotifier {
  static final InventoryService _instance = InventoryService._internal();
  factory InventoryService() => _instance;
  InventoryService._internal();

  final NotificationService _notificationService = NotificationService();
  
  // Sample inventory data - in a real app, this would come from a backend
  final List<InventoryItem> _inventoryItems = [
    InventoryItem(
      id: '1',
      name: 'Electric Plug',
      description: 'High-quality electric plug designed to enable your entry to electricity. Soft cushions, ergonomic support, and a sturdy base ensure a cozy retreat for relaxing, working, or unwinding.',
      imageUrl: 'electric_plug',
      price: 50000,
      rentalPeriod: RentalPeriod.month,
      status: InventoryStatus.rented,
      createdDate: DateTime.now().subtract(const Duration(days: 15)),
      dueDate: DateTime.now().add(const Duration(days: 15)),
      ownerName: '<PERSON>',
      hublockerName: 'HUST Hublocker',
      hublockerAddress: 'No 1b, Ta Quang Buu Street',
      rating: 4.0,
      reviewCount: 12,
      lockerNumber: 'A-01',
      productLocation: ProductLocation.inLocker,
    ),
    InventoryItem(
      id: '2',
      name: 'Modern Lemon Chair',
      description: 'Stylish and comfortable modern chair with premium materials. Perfect for office or home use with ergonomic design.',
      imageUrl: 'lemon_chair',
      price: 75000,
      rentalPeriod: RentalPeriod.month,
      status: InventoryStatus.lease,
      createdDate: DateTime.now().subtract(const Duration(days: 10)),
      ownerName: 'Sarah Wilson',
      hublockerName: 'Logic Box Golden View',
      hublockerAddress: 'Golden View Building, District 4',
      rating: 4.5,
      reviewCount: 8,
      productLocation: ProductLocation.notDelivered,
      isEditable: true,
    ),
    InventoryItem(
      id: '3',
      name: 'Gaming Headset',
      description: 'Professional gaming headset with surround sound and noise cancellation. Perfect for gaming and professional calls.',
      imageUrl: 'gaming_headset',
      price: 25000,
      rentalPeriod: RentalPeriod.day,
      status: InventoryStatus.delivery,
      createdDate: DateTime.now().subtract(const Duration(days: 3)),
      deliveryDate: DateTime.now().add(const Duration(days: 2)),
      renterName: 'Mike Johnson',
      renterPhone: '+84 987 654 321',
      hublockerName: 'HUST Hublocker',
      hublockerAddress: 'No 1b, Ta Quang Buu Street',
      rating: 4.2,
      reviewCount: 15,
      productLocation: ProductLocation.inTransit,
    ),
  ];

  List<InventoryItem> get allItems => List.unmodifiable(_inventoryItems);
  
  List<InventoryItem> getItemsByStatus(InventoryStatus status) {
    return _inventoryItems.where((item) => item.status == status).toList();
  }

  InventoryItem? getItemById(String id) {
    try {
      return _inventoryItems.firstWhere((item) => item.id == id);
    } catch (e) {
      return null;
    }
  }

  // Update item status
  void updateItemStatus(String itemId, InventoryStatus newStatus) {
    final index = _inventoryItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final oldItem = _inventoryItems[index];
      final updatedItem = oldItem.copyWith(status: newStatus);
      _inventoryItems[index] = updatedItem;
      
      // Send notification about status change
      _notificationService.notifyStatusChange(updatedItem, oldItem.status);
      
      notifyListeners();
    }
  }

  // Update item location (for lease items)
  void updateItemLocation(String itemId, ProductLocation newLocation) {
    final index = _inventoryItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final oldItem = _inventoryItems[index];
      final updatedItem = oldItem.copyWith(productLocation: newLocation);
      _inventoryItems[index] = updatedItem;
      
      // If location changed to inLocker and status is lease, notify about delivery
      if (newLocation == ProductLocation.inLocker && oldItem.status == InventoryStatus.lease) {
        _notificationService.notifyDeliveryComplete(updatedItem);
      }
      
      notifyListeners();
    }
  }

  // Update item hublocker
  void updateItemHublocker(String itemId, String hublockerName, String hublockerAddress, {String? lockerNumber}) {
    final index = _inventoryItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final updatedItem = _inventoryItems[index].copyWith(
        hublockerName: hublockerName,
        hublockerAddress: hublockerAddress,
        lockerNumber: lockerNumber,
      );
      _inventoryItems[index] = updatedItem;
      notifyListeners();
    }
  }

  // Add new inventory item
  void addItem(InventoryItem item) {
    _inventoryItems.add(item);
    notifyListeners();
  }

  // Remove inventory item
  void removeItem(String itemId) {
    _inventoryItems.removeWhere((item) => item.id == itemId);
    notifyListeners();
  }

  // Update item details
  void updateItem(String itemId, {
    String? name,
    String? description,
    double? price,
    RentalPeriod? rentalPeriod,
    String? hublockerName,
    String? hublockerAddress,
    String? lockerNumber,
    ProductLocation? productLocation,
    InventoryStatus? status,
    DateTime? dueDate,
    DateTime? deliveryDate,
    String? renterName,
    String? renterPhone,
  }) {
    final index = _inventoryItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final oldItem = _inventoryItems[index];
      final updatedItem = oldItem.copyWith(
        name: name,
        description: description,
        price: price,
        rentalPeriod: rentalPeriod,
        hublockerName: hublockerName,
        hublockerAddress: hublockerAddress,
        lockerNumber: lockerNumber,
        productLocation: productLocation,
        status: status,
        dueDate: dueDate,
        deliveryDate: deliveryDate,
        renterName: renterName,
        renterPhone: renterPhone,
      );
      _inventoryItems[index] = updatedItem;
      
      // Send notifications for important changes
      if (status != null && status != oldItem.status) {
        _notificationService.notifyStatusChange(updatedItem, oldItem.status);
      }
      
      if (productLocation != null && productLocation != oldItem.productLocation) {
        if (productLocation == ProductLocation.inLocker && oldItem.status == InventoryStatus.lease) {
          _notificationService.notifyDeliveryComplete(updatedItem);
        }
      }
      
      notifyListeners();
    }
  }

  // Simulate lease to delivery transition
  void moveLeaseToDelivery(String itemId, String renterName, String renterPhone) {
    final index = _inventoryItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final oldItem = _inventoryItems[index];
      final updatedItem = oldItem.copyWith(
        status: InventoryStatus.delivery,
        productLocation: ProductLocation.inTransit,
        renterName: renterName,
        renterPhone: renterPhone,
        deliveryDate: DateTime.now().add(const Duration(days: 1)),
      );
      _inventoryItems[index] = updatedItem;
      
      // Send notification about status change
      _notificationService.notifyStatusChange(updatedItem, oldItem.status);
      
      notifyListeners();
    }
  }

  // Simulate delivery to rented transition
  void moveDeliveryToRented(String itemId) {
    final index = _inventoryItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      final oldItem = _inventoryItems[index];
      final updatedItem = oldItem.copyWith(
        status: InventoryStatus.rented,
        productLocation: ProductLocation.inLocker,
        dueDate: DateTime.now().add(const Duration(days: 30)), // Default 30 days rental
      );
      _inventoryItems[index] = updatedItem;
      
      // Send notification about status change
      _notificationService.notifyStatusChange(updatedItem, oldItem.status);
      _notificationService.notifyDeliveryComplete(updatedItem);
      
      notifyListeners();
    }
  }

  // Check for due date reminders
  void checkDueDateReminders() {
    final now = DateTime.now();
    for (final item in _inventoryItems) {
      if (item.dueDate != null && item.status == InventoryStatus.rented) {
        final daysRemaining = item.dueDate!.difference(now).inDays;
        if (daysRemaining <= 3 && daysRemaining >= 0) {
          _notificationService.notifyDueDateApproaching(item);
        }
      }
    }
  }
}
