# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\AppData\\Local\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "F:\\Business Pizza Hackathon\\LockerHub\\lockerhub" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\AppData\\Local\\flutter"
  "PROJECT_DIR=F:\\Business Pizza Hackathon\\LockerHub\\lockerhub"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\AppData\\Local\\flutter"
  "FLUTTER_EPHEMERAL_DIR=F:\\Business Pizza Hackathon\\LockerHub\\lockerhub\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=F:\\Business Pizza Hackathon\\LockerHub\\lockerhub"
  "FLUTTER_TARGET=F:\\Business Pizza Hackathon\\LockerHub\\lockerhub\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC80NWY2ZTAwOTExMGRmNGYzNGVjMmNmOTlmNjNjZjczYjcxYjdhNDIwLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=F:\\Business Pizza Hackathon\\LockerHub\\lockerhub\\.dart_tool\\package_config.json"
)
