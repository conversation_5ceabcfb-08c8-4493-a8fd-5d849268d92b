import 'package:flutter/material.dart';
import 'package:lockerhub/screens/home_screen.dart';
import 'package:lockerhub/screens/favorite_screen.dart';
import 'package:lockerhub/screens/chat_screen.dart';
import 'package:lockerhub/screens/cart_screen.dart';
import 'package:lockerhub/screens/profile_screen.dart';
import 'package:lockerhub/screens/rent_locker_screen.dart';
import 'package:lockerhub/screens/notifications_screen.dart';
import 'package:lockerhub/screens/inventory_screen.dart';


class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  bool _isDarkMode = false;

  List<Widget> get _screens => [
    HomeScreen(isDarkMode: _isDarkMode),
    const FavoriteScreen(),
    const ChatScreen(),
    const CartScreen(),
    const ProfileScreen(),
  ];

  void _toggleTheme() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: _buildDrawer(),
      body: _screens[_currentIndex],
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: _isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
          boxShadow: [
            BoxShadow(
              color: _isDarkMode ? Colors.white.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: _isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
          selectedItemColor: _isDarkMode ? Colors.white : const Color(0xFF708871),
          unselectedItemColor: _isDarkMode ? Colors.white70 : Colors.grey,
          selectedLabelStyle: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: const TextStyle(
            fontSize: 12,
          ),
          items: [
            BottomNavigationBarItem(
              icon: Icon(
                _currentIndex == 0 ? Icons.home : Icons.home_outlined,
                size: 24,
              ),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                _currentIndex == 1 ? Icons.favorite : Icons.favorite_border,
                size: 24,
              ),
              label: 'Favorite',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                _currentIndex == 2 ? Icons.chat : Icons.chat_outlined,
                size: 24,
              ),
              label: 'Chat',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                _currentIndex == 3 ? Icons.shopping_cart : Icons.shopping_cart_outlined,
                size: 24,
              ),
              label: 'Cart',
            ),
            BottomNavigationBarItem(
              icon: Icon(
                _currentIndex == 4 ? Icons.person : Icons.person_outline,
                size: 24,
              ),
              label: 'Profile',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      backgroundColor: _isDarkMode ? Colors.black : Colors.white,
      child: Column(
        children: [
          // User Profile Header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.fromLTRB(20, 50, 20, 20),
            decoration: BoxDecoration(
              color: _isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User Avatar
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _isDarkMode ? Colors.white : const Color(0xFF708871),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    Icons.person,
                    color: _isDarkMode ? Colors.black : Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 12),
                // User Name
                Text(
                  'DangQuy',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _isDarkMode ? Colors.white : const Color(0xFF708871),
                  ),
                ),
                const SizedBox(height: 4),
                // User Email
                Text(
                  '<EMAIL>',
                  style: TextStyle(
                    fontSize: 14,
                    color: _isDarkMode ? Colors.white70 : Colors.grey,
                  ),
                ),
              ],
            ),
          ),

          // Menu Items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.home_outlined,
                  title: 'Home',
                  onTap: () {
                    Navigator.pop(context);
                    setState(() {
                      _currentIndex = 0;
                    });
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.inventory_2_outlined,
                  title: 'Rent Locker',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const RentLockerScreen(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.notifications_outlined,
                  title: 'Notifications',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationsScreen(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.inventory_outlined,
                  title: 'Inventory',
                  onTap: () {
                    Navigator.pop(context);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => InventoryScreen(
                          isDarkMode: _isDarkMode,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // Bottom Section
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: 'Logout',
                  onTap: () {
                    Navigator.pop(context);
                    // Show logout confirmation
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Logout'),
                          content: const Text('Are you sure you want to logout?'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('Cancel'),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.pop(context);
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Logout functionality coming soon!'),
                                    backgroundColor: Color(0xFF708871),
                                  ),
                                );
                              },
                              child: const Text('Logout'),
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),
                // Theme Toggle
                Row(
                  children: [
                    Icon(
                      _isDarkMode ? Icons.dark_mode : Icons.light_mode,
                      color: _isDarkMode ? Colors.white : const Color(0xFF708871),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      _isDarkMode ? 'Dark mode' : 'Light mode',
                      style: TextStyle(
                        fontSize: 16,
                        color: _isDarkMode ? Colors.white : const Color(0xFF708871),
                      ),
                    ),
                    const Spacer(),
                    Switch(
                      value: _isDarkMode,
                      onChanged: (value) => _toggleTheme(),
                      activeColor: _isDarkMode ? Colors.white : const Color(0xFF708871),
                      inactiveThumbColor: _isDarkMode ? Colors.white70 : Colors.grey,
                      inactiveTrackColor: _isDarkMode ? Colors.white30 : Colors.grey.shade300,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: _isDarkMode ? Colors.white : const Color(0xFF708871),
        size: 24,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          color: _isDarkMode ? Colors.white : const Color(0xFF708871),
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }
}
