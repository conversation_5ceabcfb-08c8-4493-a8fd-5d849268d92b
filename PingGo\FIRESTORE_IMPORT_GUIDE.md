# 🔥 Firestore Database Import Guide

## 📊 Your PingGo Database is Ready!

I've created all the database structure and sample data based on your product images. Here's how to import it into your Firestore database:

## 🚀 Quick Import Method (Recommended)

### Step 1: Access Firestore Console
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: **pinggo-351c6**
3. Navigate to **Firestore Database**

### Step 2: Create Collections Manually
Since you have a fresh database, you'll need to create the collections and add documents:

#### **Collection 1: `products`**
1. Click **"Start collection"**
2. Collection ID: `products`
3. For each product in `firestore-data/products.json`:
   - Document ID: Use the `id` field (e.g., `product_ao_mua_agribank`)
   - Copy the JSON data (excluding the `id` field)
   - Paste into Firestore

#### **Collection 2: `hublockers`**
1. Click **"Start collection"**
2. Collection ID: `hublockers`
3. For each hublocker in `firestore-data/hublockers.json`:
   - Document ID: Use the `id` field (e.g., `hublocker_bachkhoa`)
   - Copy the JSON data (excluding the `id` field)

#### **Collection 3: `users`**
1. Click **"Start collection"**
2. Collection ID: `users`
3. For each user in `firestore-data/users.json`:
   - Document ID: Use the `id` field (e.g., `user_demo_owner1`)
   - Copy the JSON data (excluding the `id` field)

## 📱 Your PingGo Products Database

### 🛍️ Products Available:
1. **Agribank Rain Jacket** - Waterproof outerwear (15,000 VND/day)
2. **Calculus 1 Textbook** - University mathematics book (8,000 VND/day)
3. **Detective Conan Manga** - Entertainment collection (5,000 VND/day)
4. **Universal Electric Plug** - Electronics adapter (3,000 VND/day)
5. **HUST University Uniform** - Official university wear (20,000 VND/day)
6. **Black Umbrella** - Weather protection (5,000 VND/day)
7. **Premium Official Umbrella** - Professional umbrella (8,000 VND/day)

### 🏢 Hublockers (Pickup Locations):
1. **HubLock Bach Khoa** - University location
2. **HubLock Van Hoa** - Cultural center
3. **iLogic Smart Locker** - 24/7 access
4. **iLogic Golden View** - Premium location

## 🔧 Database Structure

### Products Collection Schema:
```json
{
  "name": "string",
  "description": "string",
  "category": "string",
  "subcategory": "string",
  "images": ["array of image paths"],
  "pricing": {
    "daily": "number",
    "monthly": "number",
    "quarterly": "number",
    "yearly": "number"
  },
  "ownerId": "string",
  "ownerName": "string",
  "ownerPhone": "string",
  "hublocker": "string",
  "hublockerId": "string",
  "tags": ["array of strings"],
  "isAvailable": "boolean",
  "condition": "string",
  "rating": "number",
  "reviewCount": "number",
  "rentCount": "number"
}
```

### Hublockers Collection Schema:
```json
{
  "name": "string",
  "address": "string",
  "city": "string",
  "district": "string",
  "coordinates": {
    "lat": "number",
    "lng": "number"
  },
  "imageUrl": "string",
  "isActive": "boolean",
  "capacity": "number",
  "availableSlots": "number",
  "operatingHours": "string",
  "contactPhone": "string",
  "rating": "number"
}
```

## 🎯 Next Steps After Import:

1. **Verify Data**: Check that all collections and documents are created
2. **Test Queries**: Try filtering products by category
3. **Update App**: Modify your Flutter app to use these collections
4. **Add More Data**: You can easily add more products through the console

## 🔍 Direct Database Access:

**Firestore Console URL**: 
https://console.firebase.google.com/project/pinggo-351c6/firestore

You can now:
- ✅ View all products and their details
- ✅ Edit product information directly
- ✅ Add new products manually
- ✅ Manage hublocker locations
- ✅ Monitor user data
- ✅ Check rental transactions

## 💡 Pro Tips:

1. **Backup**: Export your data regularly
2. **Security**: Update Firestore rules for production
3. **Indexing**: Create indexes for common queries
4. **Images**: Consider uploading images to Firebase Storage for better performance

Your PingGo rental platform database is now ready for testing! 🚀
