PODS:
  - abseil/algorithm (1.20220623.0):
    - abseil/algorithm/algorithm (= 1.20220623.0)
    - abseil/algorithm/container (= 1.20220623.0)
  - abseil/algorithm/algorithm (1.20220623.0):
    - abseil/base/config
  - abseil/algorithm/container (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (1.20220623.0):
    - abseil/base/atomic_hook (= 1.20220623.0)
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/base_internal (= 1.20220623.0)
    - abseil/base/config (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/base/dynamic_annotations (= 1.20220623.0)
    - abseil/base/endian (= 1.20220623.0)
    - abseil/base/errno_saver (= 1.20220623.0)
    - abseil/base/fast_type_id (= 1.20220623.0)
    - abseil/base/log_severity (= 1.20220623.0)
    - abseil/base/malloc_internal (= 1.20220623.0)
    - abseil/base/prefetch (= 1.20220623.0)
    - abseil/base/pretty_function (= 1.20220623.0)
    - abseil/base/raw_logging_internal (= 1.20220623.0)
    - abseil/base/spinlock_wait (= 1.20220623.0)
    - abseil/base/strerror (= 1.20220623.0)
    - abseil/base/throw_delegate (= 1.20220623.0)
  - abseil/base/atomic_hook (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20220623.0)
  - abseil/base/core_headers (1.20220623.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (1.20220623.0):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20220623.0):
    - abseil/base/config
  - abseil/base/log_severity (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/prefetch (1.20220623.0):
    - abseil/base/config
  - abseil/base/pretty_function (1.20220623.0)
  - abseil/base/raw_logging_internal (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/cleanup/cleanup (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
  - abseil/cleanup/cleanup_internal (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
  - abseil/container/common (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (1.20220623.0):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20220623.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20220623.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/flat_hash_set (1.20220623.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20220623.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20220623.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (1.20220623.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/inlined_vector (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (1.20220623.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (1.20220623.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/debugging/debugging_internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/functional/any_invocable (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/bind_front (1.20220623.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/hash/city (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20220623.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/memory (1.20220623.0):
    - abseil/memory/memory (= 1.20220623.0)
  - abseil/memory/memory (1.20220623.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20220623.0):
    - abseil/meta/type_traits (= 1.20220623.0)
  - abseil/meta/type_traits (1.20220623.0):
    - abseil/base/config
  - abseil/numeric/bits (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20220623.0):
    - abseil/base/config
  - abseil/profiling/exponential_biased (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/sample_recorder (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random/distributions (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal/distribution_caller (1.20220623.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/fastmath (1.20220623.0):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/nonsecure_base (1.20220623.0):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20220623.0):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20220623.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20220623.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20220623.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20220623.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20220623.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20220623.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/random/internal/uniform_helper (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20220623.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20220623.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20220623.0):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20220623.0):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status/status (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/status/statusor (1.20220623.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings/cord (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/cord_internal (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/types/span
  - abseil/strings/cordz_statistics (1.20220623.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20220623.0):
    - abseil/base/config
  - abseil/strings/internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20220623.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
  - abseil/strings/strings (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization/graphcycles_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20220623.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20220623.0):
    - abseil/time/internal (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
  - abseil/time/internal (1.20220623.0):
    - abseil/time/internal/cctz (= 1.20220623.0)
  - abseil/time/internal/cctz (1.20220623.0):
    - abseil/time/internal/cctz/civil_time (= 1.20220623.0)
    - abseil/time/internal/cctz/time_zone (= 1.20220623.0)
  - abseil/time/internal/cctz/civil_time (1.20220623.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20220623.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20220623.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (1.20220623.0):
    - abseil/types/any (= 1.20220623.0)
    - abseil/types/bad_any_cast (= 1.20220623.0)
    - abseil/types/bad_any_cast_impl (= 1.20220623.0)
    - abseil/types/bad_optional_access (= 1.20220623.0)
    - abseil/types/bad_variant_access (= 1.20220623.0)
    - abseil/types/compare (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
  - abseil/types/any (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20220623.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20220623.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - BoringSSL-GRPC (0.0.24):
    - BoringSSL-GRPC/Implementation (= 0.0.24)
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Implementation (0.0.24):
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Interface (0.0.24)
  - cloud_firestore (4.8.2):
    - Firebase/Firestore (= 10.10.0)
    - firebase_core
    - Flutter
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Firebase/Auth (10.10.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.10.0)
  - Firebase/CoreOnly (10.10.0):
    - FirebaseCore (= 10.10.0)
  - Firebase/Firestore (10.10.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.10.0)
  - firebase_auth (4.6.3):
    - Firebase/Auth (= 10.10.0)
    - firebase_core
    - Flutter
  - firebase_core (2.14.0):
    - Firebase/CoreOnly (= 10.10.0)
    - Flutter
  - FirebaseAppCheckInterop (10.11.0)
  - FirebaseAuth (10.10.0):
    - FirebaseAppCheckInterop (~> 10.0)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
  - FirebaseCore (10.10.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Logger (~> 7.8)
  - FirebaseCoreInternal (10.11.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseFirestore (10.10.0):
    - abseil/algorithm (~> 1.20220623.0)
    - abseil/base (~> 1.20220623.0)
    - abseil/container/flat_hash_map (~> 1.20220623.0)
    - abseil/memory (~> 1.20220623.0)
    - abseil/meta (~> 1.20220623.0)
    - abseil/strings/strings (~> 1.20220623.0)
    - abseil/time (~> 1.20220623.0)
    - abseil/types (~> 1.20220623.0)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.50.1)"
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - GoogleUtilities/AppDelegateSwizzler (7.11.1):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.11.1):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.11.1):
    - GoogleUtilities/Environment
  - GoogleUtilities/Network (7.11.1):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.11.1)"
  - GoogleUtilities/Reachability (7.11.1):
    - GoogleUtilities/Logger
  - "gRPC-C++ (1.50.1)":
    - "gRPC-C++/Implementation (= 1.50.1)"
    - "gRPC-C++/Interface (= 1.50.1)"
  - "gRPC-C++/Implementation (1.50.1)":
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/cleanup/cleanup (= 1.20220623.0)
    - abseil/container/flat_hash_map (= 1.20220623.0)
    - abseil/container/flat_hash_set (= 1.20220623.0)
    - abseil/container/inlined_vector (= 1.20220623.0)
    - abseil/functional/any_invocable (= 1.20220623.0)
    - abseil/functional/bind_front (= 1.20220623.0)
    - abseil/functional/function_ref (= 1.20220623.0)
    - abseil/hash/hash (= 1.20220623.0)
    - abseil/memory/memory (= 1.20220623.0)
    - abseil/meta/type_traits (= 1.20220623.0)
    - abseil/random/random (= 1.20220623.0)
    - abseil/status/status (= 1.20220623.0)
    - abseil/status/statusor (= 1.20220623.0)
    - abseil/strings/cord (= 1.20220623.0)
    - abseil/strings/str_format (= 1.20220623.0)
    - abseil/strings/strings (= 1.20220623.0)
    - abseil/synchronization/synchronization (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
    - abseil/utility/utility (= 1.20220623.0)
    - "gRPC-C++/Interface (= 1.50.1)"
    - gRPC-Core (= 1.50.1)
  - "gRPC-C++/Interface (1.50.1)"
  - gRPC-Core (1.50.1):
    - gRPC-Core/Implementation (= 1.50.1)
    - gRPC-Core/Interface (= 1.50.1)
  - gRPC-Core/Implementation (1.50.1):
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/container/flat_hash_map (= 1.20220623.0)
    - abseil/container/flat_hash_set (= 1.20220623.0)
    - abseil/container/inlined_vector (= 1.20220623.0)
    - abseil/functional/any_invocable (= 1.20220623.0)
    - abseil/functional/bind_front (= 1.20220623.0)
    - abseil/functional/function_ref (= 1.20220623.0)
    - abseil/hash/hash (= 1.20220623.0)
    - abseil/memory/memory (= 1.20220623.0)
    - abseil/meta/type_traits (= 1.20220623.0)
    - abseil/random/random (= 1.20220623.0)
    - abseil/status/status (= 1.20220623.0)
    - abseil/status/statusor (= 1.20220623.0)
    - abseil/strings/cord (= 1.20220623.0)
    - abseil/strings/str_format (= 1.20220623.0)
    - abseil/strings/strings (= 1.20220623.0)
    - abseil/synchronization/synchronization (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
    - abseil/utility/utility (= 1.20220623.0)
    - BoringSSL-GRPC (= 0.0.24)
    - gRPC-Core/Interface (= 1.50.1)
  - gRPC-Core/Interface (1.50.1)
  - GTMSessionFetcher/Core (3.1.1)
  - leveldb-library (1.22.2)
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - PromisesObjC (2.2.0)

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)

SPEC REPOS:
  trunk:
    - abseil
    - BoringSSL-GRPC
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseFirestore
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter

SPEC CHECKSUMS:
  abseil: 926fb7a82dc6d2b8e1f2ed7f3a718bce691d1e46
  BoringSSL-GRPC: 3175b25143e648463a56daeaaa499c6cb86dad33
  cloud_firestore: 818ebb1a8235177a0dcf7005c14aed5408b8342c
  Firebase: facd334e557a979bd03a0b58d90fd56b52b8aba0
  firebase_auth: 9905bc3d82328b5050a8b7cb410a959f150b6549
  firebase_core: 85b6664038311940ad60584eaabc73103c61f5de
  FirebaseAppCheckInterop: 255b6c0292fe5da995c8b2df0c02f6a3ca7f61b4
  FirebaseAuth: 5ddbe23ebc4e647469261f5c59cd12a04f37c8e6
  FirebaseCore: d027ff503d37edb78db98429b11f580a24a7df2a
  FirebaseCoreInternal: 9e46c82a14a3b3a25be4e1e151ce6d21536b89c0
  FirebaseFirestore: b3bb12a497c9d13e80ec3158dbb75ded03592e8d
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  GoogleUtilities: 9aa0ad5a7bc171f8bae016300bfcfa3fb8425749
  "gRPC-C++": 0968bace703459fd3e5dcb0b2bed4c573dbff046
  gRPC-Core: 17108291d84332196d3c8466b48f016fc17d816d
  GTMSessionFetcher: e8647203b65cee28c5f73d0f473d096653945e72
  leveldb-library: f03246171cce0484482ec291f88b6d563699ee06
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  PromisesObjC: 09985d6d70fbe7878040aa746d78236e6946d2ef

PODFILE CHECKSUM: ef19549a9bc3046e7bb7d2fab4d021637c0c58a3

COCOAPODS: 1.11.3
