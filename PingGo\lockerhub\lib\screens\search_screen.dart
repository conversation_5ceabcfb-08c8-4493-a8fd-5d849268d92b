import 'package:flutter/material.dart';
import 'package:lockerhub/screens/product_detail_screen.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _sortOrder = 'relevance'; // 'asc', 'desc', 'relevance'
  
  // Sample product data
  final List<Map<String, dynamic>> _products = [
    {
      'name': 'Modern Black Chair',
      'price': 500,
      'image': 'lib/images/black_chair.jpg',
      'isFavorite': false,
    },
    {
      'name': 'Modern Lemon Chair',
      'price': 700,
      'image': 'lib/images/lemon_chair.jpg',
      'isFavorite': true,
    },
    {
      'name': 'Modern Form Chair',
      'price': 600,
      'image': 'lib/images/form_chair.jpg',
      'isFavorite': true,
    },
    {
      'name': 'Modern White Chair',
      'price': 700,
      'image': 'lib/images/white_chair.jpg',
      'isFavorite': false,
    },
  ];

  List<Map<String, dynamic>> get _sortedProducts {
    List<Map<String, dynamic>> sorted = List.from(_products);
    if (_sortOrder == 'asc') {
      sorted.sort((a, b) => a['price'].compareTo(b['price']));
    } else if (_sortOrder == 'desc') {
      sorted.sort((a, b) => b['price'].compareTo(a['price']));
    }
    return sorted;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF708871)),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Search',
          style: TextStyle(
            color: Color(0xFF708871),
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Color(0xFF708871)),
            onPressed: () {
              // TODO: Implement notifications
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Modern Chair',
                hintStyle: const TextStyle(color: Colors.grey),
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                filled: true,
                fillColor: Colors.white,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
          ),
          
          // Results count and filter
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Found ${_sortedProducts.length} results',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF708871),
                  ),
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.filter_list, color: Color(0xFF708871)),
                  onSelected: (value) {
                    setState(() {
                      _sortOrder = value;
                    });
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'relevance',
                      child: Text('Relevance'),
                    ),
                    const PopupMenuItem(
                      value: 'asc',
                      child: Text('Price: Low to High'),
                    ),
                    const PopupMenuItem(
                      value: 'desc',
                      child: Text('Price: High to Low'),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Product Grid
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.75,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: _sortedProducts.length,
                itemBuilder: (context, index) {
                  final product = _sortedProducts[index];
                  return _buildProductCard(product);
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(Map<String, dynamic> product) {
    return GestureDetector(
      onTap: () {
        // Navigate to product detail screen
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ProductDetailScreen(
              product: {
                'id': DateTime.now().millisecondsSinceEpoch.toString(),
                'name': product['name'],
                'price': product['price'],
                'image': product['image'],
                'owner': 'Joe Trump',
                'phone': '+84 123 456 789',
                'location': 'Hanoi',
                'hublocker': 'HUST Hublocker',
                'description': 'High-quality modern chair perfect for office or home use.',
                'tags': ['Furniture', 'Chair', 'Modern'],
                'rating': 4.5,
                'reviews': 12,
              },
            ),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.all(8),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Container(
              height: 80,
              width: double.infinity,
              decoration: BoxDecoration(
                color: const Color(0xFFFEF3E2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.chair,
                size: 50,
                color: Color(0xFF708871)
              ),
            ),
            const SizedBox(height: 12),

            // Product Name
            Text(
              product['name'],
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Color(0xFF708871),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),

            // Price
            Text(
              '\$${product['price']}/day',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF708871),
              ),
            ),
            const SizedBox(height: 4),

            // Rating
            Row(
              children: [
                const Icon(
                  Icons.star,
                  size: 16,
                  color: Colors.amber,
                ),
                const SizedBox(width: 4),
                const Text(
                  '4.5',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const Spacer(),
                Icon(
                  product['isFavorite'] ? Icons.favorite : Icons.favorite_border,
                  size: 16,
                  color: product['isFavorite'] ? Colors.red : Colors.grey,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
