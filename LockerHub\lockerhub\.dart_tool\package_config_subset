_flutterfire_internals
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.3/lib/
async
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0/lib/
boolean_selector
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.1/lib/
characters
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.3.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.3.0/lib/
clock
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.1/lib/
cloud_firestore
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-4.8.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-4.8.2/lib/
cloud_firestore_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-5.15.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-5.15.2/lib/
cloud_firestore_web
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-3.6.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-3.6.2/lib/
collection
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.17.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.17.1/lib/
cupertino_icons
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.5/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.5/lib/
fake_async
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.1/lib/
firebase_auth
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-4.6.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-4.6.3/lib/
firebase_auth_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-6.15.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-6.15.3/lib/
firebase_auth_web
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.5.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.5.3/lib/
firebase_core
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-2.14.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-2.14.0/lib/
firebase_core_platform_interface
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-4.8.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-4.8.0/lib/
firebase_core_web
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.6.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.6.0/lib/
flutter_lints
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-2.0.1/lib/
http
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-0.13.6/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-0.13.6/lib/
http_parser
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.0.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.0.2/lib/
js
2.19
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/js-0.6.7/lib/
lints
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-2.0.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-2.0.1/lib/
matcher
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.15/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.15/lib/
material_color_utilities
2.13
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.2.0/lib/
meta
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.9.1/lib/
path
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.8.3/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.8.3/lib/
plugin_platform_interface
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.4/lib/
source_span
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.9.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.9.1/lib/
stack_trace
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.11.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.11.0/lib/
stream_channel
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.1/lib/
string_scanner
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.2.0/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.2.0/lib/
term_glyph
2.12
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1/lib/
test_api
2.18
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.5.1/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.5.1/lib/
typed_data
2.17
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.3.2/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.3.2/lib/
vector_math
2.14
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/
file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4/lib/
sky_engine
3.0
file:///C:/Users/<USER>/AppData/Local/flutter/bin/cache/pkg/sky_engine/
file:///C:/Users/<USER>/AppData/Local/flutter/bin/cache/pkg/sky_engine/lib/
flutter
3.0
file:///C:/Users/<USER>/AppData/Local/flutter/packages/flutter/
file:///C:/Users/<USER>/AppData/Local/flutter/packages/flutter/lib/
flutter_test
3.0
file:///C:/Users/<USER>/AppData/Local/flutter/packages/flutter_test/
file:///C:/Users/<USER>/AppData/Local/flutter/packages/flutter_test/lib/
flutter_web_plugins
3.0
file:///C:/Users/<USER>/AppData/Local/flutter/packages/flutter_web_plugins/
file:///C:/Users/<USER>/AppData/Local/flutter/packages/flutter_web_plugins/lib/
lockerhub
2.19
file:///F:/Business%20Pizza%20Hackathon/LockerHub/lockerhub/
file:///F:/Business%20Pizza%20Hackathon/LockerHub/lockerhub/lib/
2
