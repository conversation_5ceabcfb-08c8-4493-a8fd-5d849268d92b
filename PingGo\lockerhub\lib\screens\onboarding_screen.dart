import 'package:flutter/material.dart';
import 'package:lockerhub/screens/auth_selection_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: "Everything you need at one place",
      subtitle: "All types of apps, charts you need at one place",
      imagePath: "lib/images/pinggo_dark.png",
    ),
    OnboardingPage(
      title: "Everything you need at one place",
      subtitle: "All types of charts you need at one place",
      imagePath: "lib/images/pinggo_dark.png",
    ),
    OnboardingPage(
      title: "Everything you need at one place",
      subtitle: "All types of tools you need at one place",
      imagePath: "lib/images/pinggo_dark.png",
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF3E2),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Skip button
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: _goToAuthSelection,
                    child: const Text(
                      'Skip',
                      style: TextStyle(
                        color: Color(0xFF708871),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // PageView
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),
            
            // Page indicators and navigation
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Page indicators
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _currentPage == index
                              ? const Color(0xFF708871)
                              : const Color(0xFF708871).withOpacity(0.3),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Navigation buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Skip button (invisible on first page)
                      SizedBox(
                        width: 80,
                        child: _currentPage > 0
                            ? TextButton(
                                onPressed: _goToAuthSelection,
                                style: TextButton.styleFrom(
                                  backgroundColor: const Color(0xFF708871),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: const Text('Skip'),
                              )
                            : null,
                      ),
                      
                      // Next/Get Started button
                      SizedBox(
                        width: 80,
                        child: ElevatedButton(
                          onPressed: _currentPage == _pages.length - 1
                              ? _goToAuthSelection
                              : _nextPage,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF708871),
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            _currentPage == _pages.length - 1 ? 'Next' : 'Next',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo/Image
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: const Color(0xFF708871),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Image.asset(
                page.imagePath,
                width: 120,
                height: 120,
                color: Colors.white,
                errorBuilder: (context, error, stackTrace) {
                  return const Icon(
                    Icons.apps,
                    size: 80,
                    color: Colors.white,
                  );
                },
              ),
            ),
          ),
          
          const SizedBox(height: 48),
          
          // Title
          Text(
            page.title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF708871),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Subtitle
          Text(
            page.subtitle,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF708871),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToAuthSelection() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const AuthSelectionScreen(),
      ),
    );
  }
}

class OnboardingPage {
  final String title;
  final String subtitle;
  final String imagePath;

  OnboardingPage({
    required this.title,
    required this.subtitle,
    required this.imagePath,
  });
}
