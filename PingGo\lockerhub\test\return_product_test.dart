import 'package:flutter_test/flutter_test.dart';
import 'package:lockerhub/models/product_damage.dart';

void main() {
  group('Product Damage Assessment Tests', () {
    test('CategoryDamageRates calculates correct damage fees', () {
      // Test electronics damage fee calculation
      const productValue = 100.0;
      
      // Test no damage
      final noDamageFee = CategoryDamageRates.calculateDamageFee(
        ProductCategory.electronics, 
        DamageLevel.none, 
        productValue
      );
      expect(noDamageFee, equals(0.0));
      
      // Test minor damage (15% for electronics)
      final minorDamageFee = CategoryDamageRates.calculateDamageFee(
        ProductCategory.electronics, 
        DamageLevel.minor, 
        productValue
      );
      expect(minorDamageFee, equals(15.0));
      
      // Test moderate damage (35% for electronics)
      final moderateDamageFee = CategoryDamageRates.calculateDamageFee(
        ProductCategory.electronics, 
        DamageLevel.moderate, 
        productValue
      );
      expect(moderateDamageFee, equals(35.0));
      
      // Test severe damage (70% for electronics)
      final severeDamageFee = CategoryDamageRates.calculateDamageFee(
        ProductCategory.electronics, 
        DamageLevel.severe, 
        productValue
      );
      expect(severeDamageFee, equals(70.0));
    });

    test('CategoryDamageRates calculates correct fees for different categories', () {
      const productValue = 100.0;
      
      // Test clothing minor damage (10%)
      final clothingFee = CategoryDamageRates.calculateDamageFee(
        ProductCategory.clothing, 
        DamageLevel.minor, 
        productValue
      );
      expect(clothingFee, equals(10.0));
      
      // Test furniture minor damage (20%)
      final furnitureFee = CategoryDamageRates.calculateDamageFee(
        ProductCategory.furniture, 
        DamageLevel.minor, 
        productValue
      );
      expect(furnitureFee, equals(20.0));
      
      // Test books minor damage (5%)
      final booksFee = CategoryDamageRates.calculateDamageFee(
        ProductCategory.books, 
        DamageLevel.minor, 
        productValue
      );
      expect(booksFee, equals(5.0));
    });

    test('ProductDamageAssessment properties work correctly', () {
      final assessment = ProductDamageAssessment(
        id: 'test_id',
        productId: 'product_123',
        productName: 'Test Product',
        category: ProductCategory.electronics,
        beforeImages: ['before1.jpg', 'before2.jpg'],
        afterImages: ['after1.jpg', 'after2.jpg'],
        similarityScore: 85.5,
        damageLevel: DamageLevel.minor,
        baseFee: 0.0,
        damageFee: 15.0,
        totalFee: 15.0,
        assessmentDate: DateTime.now(),
        description: 'Minor damage detected',
        damageDetails: ['Small scratch', 'Slight discoloration'],
      );

      expect(assessment.damageLevelText, equals('Minor Damage'));
      expect(assessment.degreeNumber, equals(2));
      expect(assessment.damageColor, equals('#FF9800'));
      expect(assessment.requiresPayment, isTrue);
    });

    test('DamageLevel enum provides correct degree numbers', () {
      expect(DamageLevel.none.index + 1, equals(1));
      expect(DamageLevel.minor.index + 1, equals(2));
      expect(DamageLevel.moderate.index + 1, equals(3));
      expect(DamageLevel.severe.index + 1, equals(4));
    });

    test('AIAnalysisResult simulation works correctly', () {
      final result = AIAnalysisResult.simulateAnalysis(
        ['before1.jpg', 'before2.jpg'],
        ['after1.jpg', 'after2.jpg', 'after3.jpg', 'after4.jpg'],
      );

      expect(result.similarityScore, greaterThanOrEqualTo(85.0));
      expect(result.similarityScore, lessThanOrEqualTo(99.0));
      expect(result.detectedDamageLevel, isA<DamageLevel>());
      expect(result.analysisDescription, isNotEmpty);
      expect(result.analysisTime, isA<DateTime>());
    });

    test('CategoryDamageRates provides correct display names', () {
      expect(
        CategoryDamageRates.getCategoryDisplayName(ProductCategory.electronics),
        equals('Electronics')
      );
      expect(
        CategoryDamageRates.getCategoryDisplayName(ProductCategory.clothing),
        equals('Clothing')
      );
      expect(
        CategoryDamageRates.getCategoryDisplayName(ProductCategory.furniture),
        equals('Furniture')
      );
      expect(
        CategoryDamageRates.getCategoryDisplayName(ProductCategory.books),
        equals('Books')
      );
      expect(
        CategoryDamageRates.getCategoryDisplayName(ProductCategory.sports),
        equals('Sports Equipment')
      );
      expect(
        CategoryDamageRates.getCategoryDisplayName(ProductCategory.household),
        equals('Household Items')
      );
    });

    test('No damage assessment does not require payment', () {
      final assessment = ProductDamageAssessment(
        id: 'test_id',
        productId: 'product_123',
        productName: 'Test Product',
        category: ProductCategory.electronics,
        beforeImages: ['before1.jpg'],
        afterImages: ['after1.jpg'],
        similarityScore: 98.0,
        damageLevel: DamageLevel.none,
        baseFee: 0.0,
        damageFee: 0.0,
        totalFee: 0.0,
        assessmentDate: DateTime.now(),
        description: 'No damage detected',
      );

      expect(assessment.requiresPayment, isFalse);
      expect(assessment.damageLevelText, equals('No Damage'));
      expect(assessment.degreeNumber, equals(1));
      expect(assessment.damageColor, equals('#4CAF50'));
    });
  });
}
