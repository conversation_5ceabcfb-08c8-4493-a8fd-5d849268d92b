import 'package:flutter/material.dart';
import 'package:lockerhub/models/inventory_item.dart';
import 'package:lockerhub/screens/locker_detail_screen.dart';

class RentLockerScreen extends StatefulWidget {
  final bool isDarkMode;
  final bool isReturnFlow;
  final InventoryItem? returnItem;

  const RentLockerScreen({
    super.key,
    this.isDarkMode = false,
    this.isReturnFlow = false,
    this.returnItem,
  });

  @override
  State<RentLockerScreen> createState() => _RentLockerScreenState();
}

class _RentLockerScreenState extends State<RentLockerScreen> {
  String? selectedHublocker;
  String searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  final List<Map<String, dynamic>> hublockers = [
    {
      'name': 'HUST Hublocker',
      'location': 'No 1b, Ta Quang Buu Street',
      'distance': '2.5 km away',
      'available': true,
      'capacity': '85%',
      'icon': Icons.school,
      'rating': 4.5,
      'image': 'lib/images/hust_hublocker.jpg',
      'availableProducts': [
        {
          'name': 'Black Umbrella',
          'owner': '<PERSON>e',
          'phone': '+**************',
          'fee': '5,000 VND',
          'image': 'umbrella'
        },
        {
          'name': 'Leather Laptop Bag',
          'owner': 'Jane Smith',
          'phone': '+**************',
          'fee': '15,000 VND',
          'image': 'laptop_bag'
        }
      ]
    },
    {
      'name': 'FPT Hublocker',
      'location': 'FPT University Campus',
      'distance': '3.1 km away',
      'available': true,
      'capacity': '67%',
      'icon': Icons.business,
      'rating': 4.2,
      'image': 'lib/images/fpt_hublocker.jpg',
      'availableProducts': []
    },
    {
      'name': 'UET Hublocker',
      'location': 'University of Engineering and Technology',
      'distance': '4.2 km away',
      'available': true,
      'capacity': '92%',
      'icon': Icons.engineering,
      'rating': 4.7,
      'image': 'lib/images/uet_hublocker.jpg',
      'availableProducts': []
    },
    {
      'name': 'Logic Box Golden View',
      'location': 'Golden View Building, District 4',
      'distance': '5.8 km away',
      'available': false,
      'capacity': '100%',
      'icon': Icons.apartment,
      'rating': 4.0,
      'image': 'lib/images/logic_box.jpg',
      'availableProducts': []
    },
  ];

  List<Map<String, dynamic>> get filteredHublockers {
    if (searchQuery.isEmpty) {
      return hublockers;
    }
    return hublockers.where((hublocker) =>
        hublocker['name'].toLowerCase().contains(searchQuery.toLowerCase())
    ).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.isReturnFlow ? 'Select Return Location' : 'Locker List',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Return Flow Header
            if (widget.isReturnFlow && widget.returnItem != null) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: widget.isDarkMode
                        ? Colors.white.withValues(alpha: 0.1)
                        : Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.assignment_return,
                      color: const Color(0xFF708871),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Returning: ${widget.returnItem!.name}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: widget.isDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          Text(
                            'Select a hublocker to drop off your item',
                            style: TextStyle(
                              fontSize: 12,
                              color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Search Bar
            Container(
              decoration: BoxDecoration(
                color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: widget.isDarkMode
                      ? Colors.white.withValues(alpha: 0.1)
                      : Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                onChanged: (value) {
                  setState(() {
                    searchQuery = value;
                  });
                },
                style: TextStyle(
                  color: widget.isDarkMode ? Colors.white : Colors.black,
                ),
                decoration: InputDecoration(
                  hintText: 'Search locker by name...',
                  hintStyle: TextStyle(
                    color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: const Color(0xFF708871),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Results count
            Text(
              'Found ${filteredHublockers.length} results',
              style: TextStyle(
                fontSize: 14,
                color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(height: 16),

            // Hublocker List
            Expanded(
              child: ListView.builder(
                itemCount: filteredHublockers.length,
                itemBuilder: (context, index) {
                  final hublocker = filteredHublockers[index];
                  return _buildHublockerCard(hublocker);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHublockerCard(Map<String, dynamic> hublocker) {
    final isAvailable = hublocker['available'] as bool;

    return GestureDetector(
      onTap: isAvailable ? () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => LockerDetailScreen(hublocker: hublocker),
          ),
        );
      } : null,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isAvailable
                ? (widget.isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300)
                : Colors.red.shade300,
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: widget.isDarkMode
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: isAvailable
                    ? const Color(0xFF708871).withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                hublocker['icon'],
                color: isAvailable ? const Color(0xFF708871) : Colors.red,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            
            // Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        hublocker['name'],
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isAvailable ? const Color(0xFF708871) : Colors.red,
                        ),
                      ),
                      const Spacer(),
                      if (!isAvailable)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'Full',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    hublocker['location'],
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        size: 16,
                        color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        hublocker['distance'],
                        style: TextStyle(
                          fontSize: 12,
                          color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        Icons.inventory,
                        size: 16,
                        color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Capacity: ${hublocker['capacity']}',
                        style: TextStyle(
                          fontSize: 12,
                          color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Arrow Indicator
            Icon(
              Icons.arrow_forward_ios,
              color: widget.isDarkMode ? Colors.grey.shade400 : const Color(0xFF708871),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
