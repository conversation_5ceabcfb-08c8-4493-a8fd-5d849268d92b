import 'package:flutter/material.dart';
import 'package:lockerhub/screens/locker_detail_screen.dart';

class AllHublockersScreen extends StatefulWidget {
  final bool isDarkMode;

  const AllHublockersScreen({
    super.key,
    this.isDarkMode = false,
  });

  @override
  State<AllHublockersScreen> createState() => _AllHublockersScreenState();
}

class _AllHublockersScreenState extends State<AllHublockersScreen> {
  String searchQuery = '';
  List<Map<String, dynamic>> allHublockers = [];
  List<Map<String, dynamic>> filteredHublockers = [];

  @override
  void initState() {
    super.initState();
    _loadHublockers();
  }

  void _loadHublockers() {
    // Sample hublocker data
    allHublockers = [
      {
        'id': '1',
        'name': 'HUST Hublocker',
        'location': 'No 1b, Ta Quang Buu Street',
        'rating': 4.8,
        'image': 'lib/images/hust_hublocker.jpg',
        'availableProducts': [
          {'name': 'Projector', 'owner': 'Viet Le', 'phone': '0123456789', 'fee': '100,000 VND/day'},
          {'name': 'Whiteboard', 'owner': 'Van Q', 'phone': '0987654321', 'fee': '40,000 VND/day'},
        ],
      },
      {
        'id': '2',
        'name': 'FPT University Hub',
        'location': 'FPT University Campus',
        'rating': 4.6,
        'image': 'lib/images/fpt_hublocker.jpg',
        'availableProducts': [
          {'name': 'Smart Coffee Maker', 'owner': 'Sarah Wilson', 'phone': '0123456789', 'fee': '30,000 VND/day'},
          {'name': 'Laptop Stand', 'owner': 'Mike Chen', 'phone': '0987654321', 'fee': '20,000 VND/day'},
        ],
      },
      {
        'id': '3',
        'name': 'Logic Box Golden View',
        'location': 'Golden View Building, District 4',
        'rating': 4.7,
        'image': 'lib/images/logic_box_hublocker.jpg',
        'availableProducts': [
          {'name': 'Professional Drill Set', 'owner': 'David Brown', 'phone': '0123456789', 'fee': '80,000 VND/day'},
          {'name': 'Tool Kit', 'owner': 'John Smith', 'phone': '0987654321', 'fee': '50,000 VND/day'},
        ],
      },
      {
        'id': '4',
        'name': 'UET Hublocker',
        'location': 'University of Engineering and Technology',
        'rating': 4.5,
        'image': 'lib/images/uet_hublocker.jpg',
        'availableProducts': [
          {'name': 'Tennis Racket', 'owner': 'Mike Johnson', 'phone': '0123456789', 'fee': '25,000 VND/day'},
          {'name': 'Sports Equipment', 'owner': 'Anna Lee', 'phone': '0987654321', 'fee': '35,000 VND/day'},
        ],
      },
      {
        'id': '5',
        'name': 'Tech Hub Central',
        'location': 'District 1, Ho Chi Minh City',
        'rating': 4.9,
        'image': 'lib/images/tech_hub_central.jpg',
        'availableProducts': [
          {'name': 'Gaming Laptop', 'owner': 'Alex Chen', 'phone': '0123456789', 'fee': '150,000 VND/day'},
          {'name': 'VR Headset', 'owner': 'Lisa Wang', 'phone': '0987654321', 'fee': '100,000 VND/day'},
        ],
      },
    ];
    
    filteredHublockers = List.from(allHublockers);
  }

  void _filterHublockers(String query) {
    setState(() {
      searchQuery = query;
      if (query.isEmpty) {
        filteredHublockers = List.from(allHublockers);
      } else {
        filteredHublockers = allHublockers.where((hublocker) {
          return hublocker['name'].toLowerCase().contains(query.toLowerCase()) ||
                 hublocker['location'].toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'All Hublockers',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          // Search Bar
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: widget.isDarkMode 
                    ? Colors.white.withOpacity(0.1) 
                    : Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TextField(
              onChanged: _filterHublockers,
              style: TextStyle(
                color: widget.isDarkMode ? Colors.white : Colors.black,
              ),
              decoration: InputDecoration(
                hintText: 'Search hublockers...',
                hintStyle: TextStyle(
                  color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                ),
                border: InputBorder.none,
                icon: Icon(
                  Icons.search,
                  color: widget.isDarkMode ? Colors.grey.shade400 : const Color(0xFF708871),
                ),
              ),
            ),
          ),

          // Results Count
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  '${filteredHublockers.length} hublocker${filteredHublockers.length != 1 ? 's' : ''} found',
                  style: TextStyle(
                    fontSize: 14,
                    color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Hublockers List
          Expanded(
            child: filteredHublockers.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: filteredHublockers.length,
                    itemBuilder: (context, index) {
                      return _buildHublockerCard(filteredHublockers[index]);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: widget.isDarkMode ? Colors.grey.shade600 : Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No hublockers found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search terms',
            style: TextStyle(
              fontSize: 14,
              color: widget.isDarkMode ? Colors.grey.shade500 : Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHublockerCard(Map<String, dynamic> hublocker) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => LockerDetailScreen(hublocker: hublocker),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: widget.isDarkMode 
                ? Colors.white.withOpacity(0.1) 
                : Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Hublocker Image
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFF708871).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.business,
                color: Color(0xFF708871),
                size: 40,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // Hublocker Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    hublocker['name'],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  
                  Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.grey, size: 16),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          hublocker['location'],
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 4),
                  
                  Row(
                    children: [
                      Icon(Icons.star, color: Colors.orange, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        hublocker['rating'].toString(),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: widget.isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${(hublocker['availableProducts'] as List).length} products',
                        style: TextStyle(
                          fontSize: 12,
                          color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            // Arrow Icon
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
            ),
          ],
        ),
      ),
    );
  }
}
