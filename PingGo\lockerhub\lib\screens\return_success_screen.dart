import 'package:flutter/material.dart';
import 'package:lockerhub/models/inventory_item.dart';
import 'package:lockerhub/models/product_damage.dart';
import 'package:lockerhub/screens/rent_locker_screen.dart';

class ReturnSuccessScreen extends StatefulWidget {
  final InventoryItem item;
  final ProductDamageAssessment damageAssessment;
  final bool isDarkMode;
  final String? paymentMethod;

  const ReturnSuccessScreen({
    super.key,
    required this.item,
    required this.damageAssessment,
    this.isDarkMode = false,
    this.paymentMethod,
  });

  @override
  State<ReturnSuccessScreen> createState() => _ReturnSuccessScreenState();
}

class _ReturnSuccessScreenState extends State<ReturnSuccessScreen>
    with TickerProviderStateMixin {
  late AnimationController _checkController;
  late AnimationController _scaleController;
  late Animation<double> _checkAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _checkController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _checkAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _checkController,
      curve: Curves.elasticOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _scaleController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _checkController.forward();
    });
  }

  @override
  void dispose() {
    _checkController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Header
              Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.popUntil(context, (route) => route.isFirst),
                    icon: Icon(
                      Icons.close,
                      color: widget.isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'Return Complete',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: widget.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 48),
                ],
              ),

              const SizedBox(height: 40),

              // Success Animation
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Success Icon
                    AnimatedBuilder(
                      animation: _scaleAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _scaleAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(60),
                              border: Border.all(
                                color: Colors.green.shade400,
                                width: 3,
                              ),
                            ),
                            child: AnimatedBuilder(
                              animation: _checkAnimation,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _checkAnimation.value,
                                  child: Icon(
                                    Icons.check,
                                    color: Colors.green.shade600,
                                    size: 60,
                                  ),
                                );
                              },
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 32),

                    // Success Message
                    Text(
                      'Return Successful!',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: widget.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),

                    const SizedBox(height: 16),

                    Text(
                      widget.damageAssessment.requiresPayment
                        ? 'Payment processed successfully. Your product return has been completed.'
                        : 'Your product return has been completed successfully.',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                        height: 1.5,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Return Summary
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: widget.isDarkMode 
                              ? Colors.white.withOpacity(0.1) 
                              : Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Return Summary',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: widget.isDarkMode ? Colors.white : Colors.black,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          _buildSummaryRow('Product:', widget.item.name),
                          _buildSummaryRow('Return Date:', _formatDate(DateTime.now())),
                          _buildSummaryRow('Condition:', widget.damageAssessment.damageLevelText),
                          _buildSummaryRow('Similarity Score:', '${widget.damageAssessment.similarityScore.toStringAsFixed(0)}%'),
                          
                          if (widget.damageAssessment.requiresPayment) ...[
                            _buildSummaryRow('Damage Fee:', '\$${widget.damageAssessment.totalFee.toStringAsFixed(2)}'),
                            if (widget.paymentMethod != null)
                              _buildSummaryRow('Payment Method:', widget.paymentMethod!),
                          ],

                          const SizedBox(height: 16),
                          
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.green.withOpacity(0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.green.shade700,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    'A confirmation email has been sent to your registered email address.',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.green.shade700,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Next Steps
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF708871).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: const Color(0xFF708871).withOpacity(0.3),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.next_plan,
                                color: const Color(0xFF708871),
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Next Step',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Now you need to rent a locker to drop off the product at your selected hublocker location.',
                            style: TextStyle(
                              fontSize: 14,
                              color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Action Buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton.icon(
                      onPressed: _rentLocker,
                      icon: const Icon(Icons.storage),
                      label: const Text('Rent a Locker'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF708871),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: OutlinedButton(
                      onPressed: () => Navigator.popUntil(context, (route) => route.isFirst),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                        side: BorderSide(
                          color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Back to Home'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: widget.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _rentLocker() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RentLockerScreen(
          isDarkMode: widget.isDarkMode,
          isReturnFlow: true,
          returnItem: widget.item,
        ),
      ),
    );
  }
}
