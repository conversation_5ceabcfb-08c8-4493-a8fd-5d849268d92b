import 'package:flutter/material.dart';
import 'package:lockerhub/models/inventory_item.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final List<AppNotification> _notifications = [];

  List<AppNotification> get notifications => List.unmodifiable(_notifications);

  // Add notification
  void addNotification(AppNotification notification) {
    _notifications.insert(0, notification);
    // Keep only last 50 notifications
    if (_notifications.length > 50) {
      _notifications.removeRange(50, _notifications.length);
    }
  }

  // Mark notification as read
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    }
  }

  // Get unread count
  int get unreadCount => _notifications.where((n) => !n.isRead).length;

  // Clear all notifications
  void clearAll() {
    _notifications.clear();
  }

  // Generate QR code for locker access
  String generateQRCode() {
    return 'PINGGO_${DateTime.now().millisecondsSinceEpoch}_${(DateTime.now().millisecondsSinceEpoch % 10000)}';
  }

  // Generate access PIN for locker
  String generateAccessPin() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return (random % 900000 + 100000).toString(); // 6-digit pin
  }

  // Add locker rental confirmation notification with QR code and PIN
  void addLockerRentalConfirmation({
    required String productName,
    required String hublockerName,
    required String hublockerAddress,
    String? relatedItemId,
  }) {
    final qrCode = generateQRCode();
    final accessPin = generateAccessPin();

    addNotification(AppNotification(
      id: 'locker_confirmed_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Locker Rental Confirmed!',
      message: 'Your rental request for "$productName" has been confirmed! Use the QR code or PIN to access your locker at $hublockerName.',
      type: NotificationType.statusChange,
      timestamp: DateTime.now(),
      priority: NotificationPriority.high,
      relatedItemId: relatedItemId,
      qrCode: qrCode,
      accessPin: accessPin,
      hublockerName: hublockerName,
      hublockerAddress: hublockerAddress,
    ));
  }

  // Add product placement notification (when owner places product in locker)
  void addProductPlacementNotification({
    required String productName,
    required String hublockerName,
    required String hublockerAddress,
    String? relatedItemId,
  }) {
    final qrCode = generateQRCode();
    final accessPin = generateAccessPin();

    addNotification(AppNotification(
      id: 'product_placed_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Product Ready for Pickup',
      message: 'Your "$productName" has been placed in the locker at $hublockerName. Use the QR code or PIN below to retrieve it.',
      type: NotificationType.statusChange,
      timestamp: DateTime.now(),
      priority: NotificationPriority.high,
      relatedItemId: relatedItemId,
      qrCode: qrCode,
      accessPin: accessPin,
      hublockerName: hublockerName,
      hublockerAddress: hublockerAddress,
    ));
  }

  // Inventory-specific notification methods
  void notifyDueDateApproaching(InventoryItem item) {
    final daysRemaining = item.daysRemaining ?? 0;
    String message;
    
    if (daysRemaining <= 1) {
      message = 'Your rental "${item.name}" is due tomorrow! Please return it to ${item.hublockerName}.';
    } else {
      message = 'Your rental "${item.name}" is due in $daysRemaining days. Please prepare for return.';
    }

    addNotification(AppNotification(
      id: 'due_date_${item.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Rental Due Date Reminder',
      message: message,
      type: NotificationType.dueDate,
      timestamp: DateTime.now(),
      relatedItemId: item.id,
    ));
  }

  void notifyItemOverdue(InventoryItem item) {
    addNotification(AppNotification(
      id: 'overdue_${item.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Item Overdue',
      message: 'Your rental "${item.name}" is overdue! Please return it immediately to avoid additional charges.',
      type: NotificationType.overdue,
      timestamp: DateTime.now(),
      relatedItemId: item.id,
      priority: NotificationPriority.high,
    ));
  }

  void notifyDeliveryComplete(InventoryItem item) {
    String message;
    if (item.status == InventoryStatus.rented) {
      message = 'Your rental "${item.name}" has been delivered to ${item.hublockerName}. You can now pick it up from locker ${item.lockerNumber}.';
    } else {
      message = 'Your item "${item.name}" has been delivered to ${item.hublockerName} and is now available for rent.';
    }

    addNotification(AppNotification(
      id: 'delivery_${item.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Delivery Complete',
      message: message,
      type: NotificationType.delivery,
      timestamp: DateTime.now(),
      relatedItemId: item.id,
    ));
  }

  void notifyStatusChange(InventoryItem item, InventoryStatus oldStatus) {
    String message;
    switch (item.status) {
      case InventoryStatus.rented:
        message = 'Your item "${item.name}" is now being rented by ${item.renterName}.';
        break;
      case InventoryStatus.lease:
        message = 'Your item "${item.name}" is now available for lease at ${item.hublockerName}.';
        break;
      case InventoryStatus.delivery:
        message = 'Your item "${item.name}" is now in delivery to ${item.hublockerName}.';
        break;
    }

    addNotification(AppNotification(
      id: 'status_${item.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Status Update',
      message: message,
      type: NotificationType.statusChange,
      timestamp: DateTime.now(),
      relatedItemId: item.id,
    ));
  }

  void notifyRentalCancelled(InventoryItem item, double refundAmount) {
    addNotification(AppNotification(
      id: 'cancel_${item.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Rental Cancelled',
      message: 'Your ${item.status == InventoryStatus.lease ? 'lease' : 'rental'} for "${item.name}" has been cancelled. Refund of ${refundAmount.toStringAsFixed(0)} VND will be processed within 3-5 business days.',
      type: NotificationType.cancellation,
      timestamp: DateTime.now(),
      relatedItemId: item.id,
    ));
  }

  void notifyPaymentProcessed(String itemName, double amount, String type) {
    addNotification(AppNotification(
      id: 'payment_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Payment Processed',
      message: '$type of ${amount.toStringAsFixed(0)} VND for "$itemName" has been processed successfully.',
      type: NotificationType.payment,
      timestamp: DateTime.now(),
    ));
  }

  // Rental request notifications
  void notifyRentalRequest(String productName, String renterName, String requestId) {
    addNotification(AppNotification(
      id: 'rental_request_$requestId',
      title: 'New Rental Request',
      message: '$renterName wants to rent your "$productName". Tap to review and respond.',
      type: NotificationType.statusChange,
      timestamp: DateTime.now(),
      priority: NotificationPriority.high,
      relatedItemId: requestId,
    ));
  }

  void notifyRentalConfirmed(String productName, String qrCode, String accessPin) {
    addNotification(AppNotification(
      id: 'rental_confirmed_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Rental Confirmed!',
      message: 'Your rental request for "$productName" has been confirmed! QR Code: $qrCode, Access Pin: $accessPin',
      type: NotificationType.statusChange,
      timestamp: DateTime.now(),
      priority: NotificationPriority.high,
    ));
  }

  void notifyProductStatusChanged(String productName, String newStatus) {
    addNotification(AppNotification(
      id: 'status_change_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Product Status Updated',
      message: 'Your product "$productName" status has been changed to $newStatus.',
      type: NotificationType.statusChange,
      timestamp: DateTime.now(),
    ));
  }

  // Initialize sample notifications for demo
  void initializeSampleNotifications() {
    addNotification(AppNotification(
      id: 'sample_1',
      title: 'New Rental Request',
      message: 'Nguyen Minh Tuan wants to rent your "Electric Plug". Tap to review and respond.',
      type: NotificationType.statusChange,
      timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
      priority: NotificationPriority.high,
    ));

    addNotification(AppNotification(
      id: 'sample_2',
      title: 'Rental Due Tomorrow',
      message: 'Your rental "Electric Plug" is due tomorrow. Please return it to HUST Hublocker.',
      type: NotificationType.dueDate,
      timestamp: DateTime.now().subtract(const Duration(hours: 6)),
      priority: NotificationPriority.high,
    ));

    addNotification(AppNotification(
      id: 'sample_3',
      title: 'Delivery Complete',
      message: 'Your item "Calculus Book" has been delivered to Logic Box Golden View.',
      type: NotificationType.delivery,
      timestamp: DateTime.now().subtract(const Duration(days: 1)),
    ));

    addNotification(AppNotification(
      id: 'sample_4',
      title: 'QR Code Generated',
      message: 'QR Code and access pin generated for your confirmed rental. Code: PINGGO_123456, Pin: 789012',
      type: NotificationType.statusChange,
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      priority: NotificationPriority.high,
    ));
  }
}

enum NotificationType {
  general,
  dueDate,
  overdue,
  delivery,
  statusChange,
  cancellation,
  payment,
}

enum NotificationPriority {
  low,
  normal,
  high,
}

class AppNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final bool isRead;
  final NotificationPriority priority;
  final String? relatedItemId;
  final String? qrCode;
  final String? accessPin;
  final String? hublockerName;
  final String? hublockerAddress;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.isRead = false,
    this.priority = NotificationPriority.normal,
    this.relatedItemId,
    this.qrCode,
    this.accessPin,
    this.hublockerName,
    this.hublockerAddress,
  });

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    bool? isRead,
    NotificationPriority? priority,
    String? relatedItemId,
    String? qrCode,
    String? accessPin,
    String? hublockerName,
    String? hublockerAddress,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      priority: priority ?? this.priority,
      relatedItemId: relatedItemId ?? this.relatedItemId,
      qrCode: qrCode ?? this.qrCode,
      accessPin: accessPin ?? this.accessPin,
      hublockerName: hublockerName ?? this.hublockerName,
      hublockerAddress: hublockerAddress ?? this.hublockerAddress,
    );
  }

  IconData get icon {
    switch (type) {
      case NotificationType.general:
        return Icons.info_outline;
      case NotificationType.dueDate:
        return Icons.schedule;
      case NotificationType.overdue:
        return Icons.warning;
      case NotificationType.delivery:
        return Icons.local_shipping;
      case NotificationType.statusChange:
        return Icons.update;
      case NotificationType.cancellation:
        return Icons.cancel_outlined;
      case NotificationType.payment:
        return Icons.payment;
    }
  }

  Color get color {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.normal:
        return const Color(0xFF708871);
      case NotificationPriority.high:
        return Colors.red;
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}
