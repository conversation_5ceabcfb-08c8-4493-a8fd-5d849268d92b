import 'package:flutter/material.dart';
import 'package:lockerhub/screens/splash_screen.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

Future<void> main() async {

  // Initialize connection to Firebase Database for Authentication.
  // App will not work without this initialized first.
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with proper configuration
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Entire app.
  runApp(const PingGoApp());
}

class PingGoApp extends StatelessWidget {
  const PingGoApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'PingGo',
      theme: ThemeData(
        primaryColor: const Color(0xFF708871),
        scaffoldBackgroundColor: const Color(0xFFFEF3E2),
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF708871),
          surface: const Color(0xFFFEF3E2),
        ),
        useMaterial3: true,
      ),
      home: const SplashScreen(),
    );
  }
}






