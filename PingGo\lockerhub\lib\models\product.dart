enum ProductCategory {
  electronics,
  furniture,
  clothing,
  books,
  sports,
  household,
  appliances,
  tools,
  automotive,
  gaming,
}

enum RentalPeriod {
  day,
  week,
  month,
  quarter,
  year,
}

class ProductPricing {
  final RentalPeriod period;
  final double price;
  final String label;
  final String displayText;

  ProductPricing({
    required this.period,
    required this.price,
    required this.label,
    required this.displayText,
  });

  Map<String, dynamic> toJson() {
    return {
      'period': period.name,
      'price': price,
      'label': label,
      'displayText': displayText,
    };
  }

  factory ProductPricing.fromJson(Map<String, dynamic> json) {
    return ProductPricing(
      period: RentalPeriod.values.firstWhere((e) => e.name == json['period']),
      price: json['price'].toDouble(),
      label: json['label'],
      displayText: json['displayText'],
    );
  }
}

class ProductTag {
  final String id;
  final String name;
  final String color;

  ProductTag({
    required this.id,
    required this.name,
    required this.color,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'color': color,
    };
  }

  factory ProductTag.fromJson(Map<String, dynamic> json) {
    return ProductTag(
      id: json['id'],
      name: json['name'],
      color: json['color'],
    );
  }
}

class ProductReview {
  final String id;
  final String userName;
  final String userAvatar;
  final double rating;
  final String comment;
  final DateTime date;
  final List<String> images;

  ProductReview({
    required this.id,
    required this.userName,
    required this.userAvatar,
    required this.rating,
    required this.comment,
    required this.date,
    this.images = const [],
  });

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }
}

class Product {
  final String id;
  final String name;
  final String description;
  final List<String> images;
  final ProductCategory category;
  final List<ProductCategory> additionalCategories;
  final List<ProductTag> tags;
  final List<ProductPricing> pricing;
  final String ownerId;
  final String ownerName;
  final String ownerAvatar;
  final String location;
  final String hublockerName;
  final String hublockerAddress;
  final double rating;
  final int reviewCount;
  final List<ProductReview> reviews;
  final bool isAvailable;
  final bool isFeatured;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> specifications;
  final int availableQuantity;
  final double latitude;
  final double longitude;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.images,
    required this.category,
    this.additionalCategories = const [],
    this.tags = const [],
    required this.pricing,
    required this.ownerId,
    required this.ownerName,
    required this.ownerAvatar,
    required this.location,
    required this.hublockerName,
    required this.hublockerAddress,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.reviews = const [],
    this.isAvailable = true,
    this.isFeatured = false,
    required this.createdAt,
    required this.updatedAt,
    this.specifications = const {},
    this.availableQuantity = 1,
    this.latitude = 0.0,
    this.longitude = 0.0,
  });

  // Get all categories including additional ones
  List<ProductCategory> get allCategories => [category, ...additionalCategories];

  // Get pricing for specific period
  ProductPricing? getPricing(RentalPeriod period) {
    try {
      return pricing.firstWhere((p) => p.period == period);
    } catch (e) {
      return null;
    }
  }

  // Get cheapest pricing option
  ProductPricing get cheapestPricing {
    return pricing.reduce((a, b) => a.price < b.price ? a : b);
  }

  // Get most expensive pricing option
  ProductPricing get mostExpensivePricing {
    return pricing.reduce((a, b) => a.price > b.price ? a : b);
  }

  // Check if product belongs to category
  bool belongsToCategory(ProductCategory cat) {
    return allCategories.contains(cat);
  }

  // Check if product has tag
  bool hasTag(String tagName) {
    return tags.any((tag) => tag.name.toLowerCase() == tagName.toLowerCase());
  }

  // Get display price range
  String get priceRange {
    if (pricing.isEmpty) return 'Price not available';
    if (pricing.length == 1) {
      return '${pricing.first.price.toStringAsFixed(0)} VND/${pricing.first.label.toLowerCase()}';
    }
    
    final cheapest = cheapestPricing;
    final mostExpensive = mostExpensivePricing;
    
    return '${cheapest.price.toStringAsFixed(0)} - ${mostExpensive.price.toStringAsFixed(0)} VND';
  }

  // Get category display name
  String get categoryDisplayName {
    switch (category) {
      case ProductCategory.electronics:
        return 'Electronics';
      case ProductCategory.furniture:
        return 'Furniture';
      case ProductCategory.clothing:
        return 'Clothing';
      case ProductCategory.books:
        return 'Books';
      case ProductCategory.sports:
        return 'Sports';
      case ProductCategory.household:
        return 'Household';
      case ProductCategory.appliances:
        return 'Appliances';
      case ProductCategory.tools:
        return 'Tools';
      case ProductCategory.automotive:
        return 'Automotive';
      case ProductCategory.gaming:
        return 'Gaming';
    }
  }

  // Get category icon
  String get categoryIcon {
    switch (category) {
      case ProductCategory.electronics:
        return 'electronics';
      case ProductCategory.furniture:
        return 'furniture';
      case ProductCategory.clothing:
        return 'clothing';
      case ProductCategory.books:
        return 'books';
      case ProductCategory.sports:
        return 'sports';
      case ProductCategory.household:
        return 'household';
      case ProductCategory.appliances:
        return 'appliances';
      case ProductCategory.tools:
        return 'tools';
      case ProductCategory.automotive:
        return 'automotive';
      case ProductCategory.gaming:
        return 'gaming';
    }
  }

  // Copy with method
  Product copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? images,
    ProductCategory? category,
    List<ProductCategory>? additionalCategories,
    List<ProductTag>? tags,
    List<ProductPricing>? pricing,
    String? ownerId,
    String? ownerName,
    String? ownerAvatar,
    String? location,
    String? hublockerName,
    String? hublockerAddress,
    double? rating,
    int? reviewCount,
    List<ProductReview>? reviews,
    bool? isAvailable,
    bool? isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? specifications,
    int? availableQuantity,
    double? latitude,
    double? longitude,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      images: images ?? this.images,
      category: category ?? this.category,
      additionalCategories: additionalCategories ?? this.additionalCategories,
      tags: tags ?? this.tags,
      pricing: pricing ?? this.pricing,
      ownerId: ownerId ?? this.ownerId,
      ownerName: ownerName ?? this.ownerName,
      ownerAvatar: ownerAvatar ?? this.ownerAvatar,
      location: location ?? this.location,
      hublockerName: hublockerName ?? this.hublockerName,
      hublockerAddress: hublockerAddress ?? this.hublockerAddress,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      reviews: reviews ?? this.reviews,
      isAvailable: isAvailable ?? this.isAvailable,
      isFeatured: isFeatured ?? this.isFeatured,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      specifications: specifications ?? this.specifications,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
    );
  }
}
