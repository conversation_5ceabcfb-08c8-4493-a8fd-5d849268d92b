import 'package:flutter/material.dart';
import 'package:lockerhub/screens/rental_success_screen.dart';

class LockerRentScreen extends StatefulWidget {
  final Map<String, dynamic> hublocker;

  const LockerRentScreen({super.key, required this.hublocker});

  @override
  State<LockerRentScreen> createState() => _LockerRentScreenState();
}

class _LockerRentScreenState extends State<LockerRentScreen> {
  String? selectedLockerNumber;
  final TextEditingController _receiverNameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  // Sample available locker numbers with sizes and fees
  final List<Map<String, dynamic>> availableLockers = [
    {
      'number': 'A-01',
      'size': '30cm x 30cm x 40cm',
      'fee': '5,000 VND',
      'available': true,
    },
    {
      'number': 'A-02',
      'size': '30cm x 30cm x 40cm',
      'fee': '5,000 VND',
      'available': false,
    },
    {
      'number': 'B-01',
      'size': '40cm x 40cm x 50cm',
      'fee': '8,000 VND',
      'available': true,
    },
    {
      'number': 'B-02',
      'size': '40cm x 40cm x 50cm',
      'fee': '8,000 VND',
      'available': true,
    },
    {
      'number': 'C-01',
      'size': '50cm x 50cm x 60cm',
      'fee': '12,000 VND',
      'available': true,
    },
  ];

  @override
  void dispose() {
    _receiverNameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF708871)),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Hublocker Rent',
          style: TextStyle(
            color: Color(0xFF708871),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hublocker Info Card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.hublocker['name'],
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF708871),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(
                          Icons.location_on,
                          color: Colors.grey,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            widget.hublocker['location'],
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Locker Detail Section
              const Text(
                'Locker Detail',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF708871),
                ),
              ),

              const SizedBox(height: 16),

              // Locker Number Selection
              const Text(
                'Locker Number:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),

              const SizedBox(height: 12),

              // Locker Grid
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  childAspectRatio: 2.5,
                ),
                itemCount: availableLockers.length,
                itemBuilder: (context, index) {
                  final locker = availableLockers[index];
                  final isSelected = selectedLockerNumber == locker['number'];
                  final isAvailable = locker['available'] as bool;

                  return GestureDetector(
                    onTap: isAvailable ? () {
                      setState(() {
                        selectedLockerNumber = locker['number'];
                      });
                    } : null,
                    child: Container(
                      decoration: BoxDecoration(
                        color: isAvailable 
                            ? (isSelected ? const Color(0xFF708871) : Colors.white)
                            : Colors.grey.shade200,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected 
                              ? const Color(0xFF708871)
                              : isAvailable 
                                  ? Colors.grey.shade300
                                  : Colors.grey.shade400,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          locker['number'],
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isSelected 
                                ? Colors.white
                                : isAvailable 
                                    ? const Color(0xFF708871)
                                    : Colors.grey,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 24),

              // Selected Locker Details
              if (selectedLockerNumber != null) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: const Color(0xFF708871)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Selected Locker: $selectedLockerNumber',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF708871),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Size: ${_getSelectedLockerDetails()['size']}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Fee: ${_getSelectedLockerDetails()['fee']}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF708871),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Receiver Information
                const Text(
                  'Receiver Information',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),

                const SizedBox(height: 16),

                // Receiver Name
                TextField(
                  controller: _receiverNameController,
                  decoration: InputDecoration(
                    labelText: 'Receiver Name',
                    hintText: 'Enter receiver name',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF708871)),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Phone Number
                TextField(
                  controller: _phoneController,
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    labelText: 'Phone Number',
                    hintText: 'Enter phone number',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF708871)),
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // Confirmation Button
                Container(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _canConfirm() ? _confirmRental : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF708871),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      disabledBackgroundColor: Colors.grey.shade300,
                    ),
                    child: const Text(
                      'Confirmation',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getSelectedLockerDetails() {
    return availableLockers.firstWhere(
      (locker) => locker['number'] == selectedLockerNumber,
      orElse: () => {},
    );
  }

  bool _canConfirm() {
    return selectedLockerNumber != null &&
           _receiverNameController.text.trim().isNotEmpty &&
           _phoneController.text.trim().isNotEmpty;
  }

  void _confirmRental() {
    final rentalData = {
      'hublocker': widget.hublocker,
      'lockerNumber': selectedLockerNumber,
      'lockerDetails': _getSelectedLockerDetails(),
      'receiverName': _receiverNameController.text.trim(),
      'phoneNumber': _phoneController.text.trim(),
      'rentalDate': DateTime.now(),
    };

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RentalSuccessScreen(rentalData: rentalData),
      ),
    );
  }
}
