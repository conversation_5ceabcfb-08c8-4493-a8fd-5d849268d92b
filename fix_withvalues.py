#!/usr/bin/env python3
"""
Script to fix withValues(alpha: x) calls to withOpacity(x) in Flutter Dart files
"""

import os
import re
import glob

def fix_withvalues_in_file(file_path):
    """Fix withValues calls in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Pattern to match .withValues(alpha: x) and replace with .withOpacity(x)
        pattern = r'\.withValues\(alpha:\s*([0-9.]+)\)'
        replacement = r'.withOpacity(\1)'
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"Fixed: {file_path}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix all Dart files"""
    # Find all .dart files in lib directory
    dart_files = glob.glob('lib/**/*.dart', recursive=True)
    
    fixed_count = 0
    for dart_file in dart_files:
        if fix_withvalues_in_file(dart_file):
            fixed_count += 1
    
    print(f"\nFixed {fixed_count} files out of {len(dart_files)} total Dart files.")

if __name__ == "__main__":
    main()
