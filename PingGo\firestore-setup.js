// Run this script to populate your Firestore with PingGo rental platform data
// Usage: node firestore-setup.js

const admin = require('firebase-admin');

// Initialize Firebase Admin with project ID
try {
  admin.initializeApp({
    projectId: 'pinggo-351c6'
  });
} catch (error) {
  console.log('Firebase already initialized or using default credentials');
}

const db = admin.firestore();

async function setupPingGoData() {
  try {
    console.log('Setting up PingGo rental platform data...');

    // Create Hublockers (Pickup Locations)
    const hublockers = [
      {
        id: 'hublocker_bachkhoa',
        name: '<PERSON><PERSON><PERSON><PERSON> Bach Khoa',
        address: 'Bach Khoa University, Hanoi',
        city: 'Hanoi',
        district: 'Hai Ba Trung',
        coordinates: { lat: 21.0058, lng: 105.8431 },
        imageUrl: 'lib/images/locker/HubLockBachKhoa.jpg',
        isActive: true,
        capacity: 50,
        availableSlots: 35,
        operatingHours: '6:00 AM - 10:00 PM',
        contactPhone: '+84 24 3868 3008',
        rating: 4.5,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'hublocker_vanhoa',
        name: '<PERSON><PERSON><PERSON><PERSON> Hoa',
        address: 'Van Hoa Cultural Center, Hanoi',
        city: 'Hanoi',
        district: 'Dong Da',
        coordinates: { lat: 21.0245, lng: 105.8412 },
        imageUrl: 'lib/images/locker/HubLockVanHoa.jpg',
        isActive: true,
        capacity: 40,
        availableSlots: 28,
        operatingHours: '7:00 AM - 9:00 PM',
        contactPhone: '+84 24 3736 2663',
        rating: 4.3,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'hublocker_ilogic',
        name: 'iLogic Smart Locker',
        address: 'iLogic Building, Hanoi',
        city: 'Hanoi',
        district: 'Cau Giay',
        coordinates: { lat: 21.0285, lng: 105.7774 },
        imageUrl: 'lib/images/locker/iLogic.jpg',
        isActive: true,
        capacity: 60,
        availableSlots: 42,
        operatingHours: '24/7',
        contactPhone: '+84 24 3755 6677',
        rating: 4.7,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'hublocker_ilogic_goldenview',
        name: 'iLogic Golden View',
        address: 'Golden View Tower, Hanoi',
        city: 'Hanoi',
        district: 'Nam Tu Liem',
        coordinates: { lat: 21.0378, lng: 105.7804 },
        imageUrl: 'lib/images/locker/iLogicBoxGoldenView.jpg',
        isActive: true,
        capacity: 45,
        availableSlots: 30,
        operatingHours: '6:00 AM - 11:00 PM',
        contactPhone: '+84 24 3755 6688',
        rating: 4.4,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      }
    ];

    // Add hublockers to Firestore
    for (const hublocker of hublockers) {
      await db.collection('hublockers').doc(hublocker.id).set(hublocker);
      console.log(`✅ Created hublocker: ${hublocker.name}`);
    }

    // Create Products for Rental
    const products = [
      {
        id: 'product_ao_mua_agribank',
        name: 'Agribank Rain Jacket',
        description: 'High-quality waterproof rain jacket from Agribank. Perfect for rainy days and outdoor activities.',
        category: 'Clothing',
        subcategory: 'Outerwear',
        images: ['lib/images/product/AoMuaAgribank.jpg'],
        pricing: {
          daily: 15000,    // 15,000 VND per day
          monthly: 300000, // 300,000 VND per month
          quarterly: 800000, // 800,000 VND per quarter
          yearly: 2500000  // 2,500,000 VND per year
        },
        ownerId: 'user_demo_owner1',
        ownerName: 'Nguyen Van A',
        ownerPhone: '+84 987 654 321',
        hublocker: 'hublocker_bachkhoa',
        hublockerId: 'hublocker_bachkhoa',
        tags: ['waterproof', 'outdoor', 'rain', 'jacket', 'agribank'],
        isAvailable: true,
        condition: 'excellent',
        size: 'L',
        color: 'Blue',
        brand: 'Agribank',
        rating: 4.6,
        reviewCount: 12,
        rentCount: 25,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'product_calculus_textbook',
        name: 'Calculus 1 Textbook',
        description: 'Essential Calculus 1 textbook for university students. Comprehensive coverage of differential and integral calculus.',
        category: 'Education',
        subcategory: 'Textbooks',
        images: ['lib/images/product/Calculus1.jpg'],
        pricing: {
          daily: 8000,     // 8,000 VND per day
          monthly: 150000, // 150,000 VND per month
          quarterly: 400000, // 400,000 VND per quarter
          yearly: 1200000  // 1,200,000 VND per year
        },
        ownerId: 'user_demo_owner2',
        ownerName: 'Tran Thi B',
        ownerPhone: '+84 912 345 678',
        hublocker: 'hublocker_vanhoa',
        hublockerId: 'hublocker_vanhoa',
        tags: ['textbook', 'calculus', 'math', 'university', 'education'],
        isAvailable: true,
        condition: 'good',
        subject: 'Mathematics',
        level: 'University',
        edition: '10th Edition',
        rating: 4.4,
        reviewCount: 8,
        rentCount: 18,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'product_conan_manga',
        name: 'Detective Conan Manga Collection',
        description: 'Popular Detective Conan manga series. Great for entertainment and Japanese language learning.',
        category: 'Entertainment',
        subcategory: 'Books & Manga',
        images: ['lib/images/product/Conan.jpg'],
        pricing: {
          daily: 5000,     // 5,000 VND per day
          monthly: 100000, // 100,000 VND per month
          quarterly: 250000, // 250,000 VND per quarter
          yearly: 800000   // 800,000 VND per year
        },
        ownerId: 'user_demo_owner3',
        ownerName: 'Le Van C',
        ownerPhone: '+84 901 234 567',
        hublocker: 'hublocker_ilogic',
        hublockerId: 'hublocker_ilogic',
        tags: ['manga', 'detective', 'conan', 'entertainment', 'japanese'],
        isAvailable: true,
        condition: 'excellent',
        language: 'Vietnamese',
        genre: 'Mystery/Detective',
        volumes: '1-20',
        rating: 4.8,
        reviewCount: 15,
        rentCount: 32,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'product_electric_plug',
        name: 'Universal Electric Plug Adapter',
        description: 'Multi-purpose electric plug adapter suitable for various devices. Essential for travelers and students.',
        category: 'Electronics',
        subcategory: 'Accessories',
        images: ['lib/images/product/ElectricPlug.jpg'],
        pricing: {
          daily: 3000,     // 3,000 VND per day
          monthly: 60000,  // 60,000 VND per month
          quarterly: 150000, // 150,000 VND per quarter
          yearly: 500000   // 500,000 VND per year
        },
        ownerId: 'user_demo_owner4',
        ownerName: 'Pham Van D',
        ownerPhone: '+84 923 456 789',
        hublocker: 'hublocker_ilogic_goldenview',
        hublockerId: 'hublocker_ilogic_goldenview',
        tags: ['electronics', 'adapter', 'plug', 'universal', 'travel'],
        isAvailable: true,
        condition: 'excellent',
        type: 'Universal Adapter',
        compatibility: 'Multiple devices',
        voltage: '100-240V',
        rating: 4.5,
        reviewCount: 20,
        rentCount: 45,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'product_hust_uniform',
        name: 'HUST University Uniform',
        description: 'Official HUST (Hanoi University of Science and Technology) uniform. Perfect for university events and ceremonies.',
        category: 'Clothing',
        subcategory: 'Uniforms',
        images: ['lib/images/product/HUSTuniform.jpg', 'lib/images/product/HUSTuniformAfter.jpg'],
        pricing: {
          daily: 20000,    // 20,000 VND per day
          monthly: 400000, // 400,000 VND per month
          quarterly: 1000000, // 1,000,000 VND per quarter
          yearly: 3000000  // 3,000,000 VND per year
        },
        ownerId: 'user_demo_owner5',
        ownerName: 'Hoang Thi E',
        ownerPhone: '+84 934 567 890',
        hublocker: 'hublocker_bachkhoa',
        hublockerId: 'hublocker_bachkhoa',
        tags: ['uniform', 'hust', 'university', 'formal', 'ceremony'],
        isAvailable: true,
        condition: 'good',
        size: 'M',
        university: 'HUST',
        type: 'Official Uniform',
        rating: 4.3,
        reviewCount: 7,
        rentCount: 12,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'product_umbrella_black',
        name: 'Black Umbrella',
        description: 'Sturdy black umbrella perfect for rainy weather. Compact and easy to carry.',
        category: 'Accessories',
        subcategory: 'Weather Protection',
        images: ['lib/images/product/Umbrella_black.jpg', 'lib/images/product/Umbrella.jpg'],
        pricing: {
          daily: 5000,     // 5,000 VND per day
          monthly: 80000,  // 80,000 VND per month
          quarterly: 200000, // 200,000 VND per quarter
          yearly: 600000   // 600,000 VND per year
        },
        ownerId: 'user_demo_owner6',
        ownerName: 'Vu Van F',
        ownerPhone: '+84 945 678 901',
        hublocker: 'hublocker_vanhoa',
        hublockerId: 'hublocker_vanhoa',
        tags: ['umbrella', 'rain', 'weather', 'protection', 'compact'],
        isAvailable: true,
        condition: 'excellent',
        color: 'Black',
        size: 'Standard',
        material: 'Polyester',
        rating: 4.7,
        reviewCount: 25,
        rentCount: 58,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        id: 'product_umbrella_official',
        name: 'Official Premium Umbrella',
        description: 'High-quality premium umbrella with official branding. Durable and stylish for professional use.',
        category: 'Accessories',
        subcategory: 'Weather Protection',
        images: ['lib/images/product/Umbrella_official.png'],
        pricing: {
          daily: 8000,     // 8,000 VND per day
          monthly: 120000, // 120,000 VND per month
          quarterly: 300000, // 300,000 VND per quarter
          yearly: 900000   // 900,000 VND per year
        },
        ownerId: 'user_demo_owner7',
        ownerName: 'Dao Thi G',
        ownerPhone: '+84 956 789 012',
        hublocker: 'hublocker_ilogic',
        hublockerId: 'hublocker_ilogic',
        tags: ['umbrella', 'premium', 'official', 'professional', 'durable'],
        isAvailable: true,
        condition: 'excellent',
        type: 'Premium',
        brand: 'Official',
        warranty: '1 year',
        rating: 4.9,
        reviewCount: 18,
        rentCount: 35,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      }
    ];

    // Add products to Firestore
    for (const product of products) {
      await db.collection('products').doc(product.id).set(product);
      console.log(`✅ Created product: ${product.name}`);
    }

    // Create Demo Users
    const users = [
      {
        id: 'user_demo_owner1',
        firstName: 'Nguyen Van',
        lastName: 'A',
        email: '<EMAIL>',
        phone: '+84 987 654 321',
        address: 'Hanoi, Vietnam',
        joinedDate: admin.firestore.FieldValue.serverTimestamp(),
        isVerified: true,
        rating: 4.8,
        totalRentals: 25,
        totalEarnings: 2500000
      },
      {
        id: 'user_demo_owner2',
        firstName: 'Tran Thi',
        lastName: 'B',
        email: '<EMAIL>',
        phone: '+84 912 345 678',
        address: 'Hanoi, Vietnam',
        joinedDate: admin.firestore.FieldValue.serverTimestamp(),
        isVerified: true,
        rating: 4.6,
        totalRentals: 18,
        totalEarnings: 1800000
      }
    ];

    // Add users to Firestore
    for (const user of users) {
      await db.collection('users').doc(user.id).set(user);
      console.log(`✅ Created user: ${user.firstName} ${user.lastName}`);
    }

    // Create Sample Reviews
    const reviews = [
      {
        productId: 'product_ao_mua_agribank',
        userId: 'user_demo_renter1',
        userName: 'Le Van C',
        rating: 5,
        comment: 'Excellent rain jacket! Kept me completely dry during heavy rain.',
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        productId: 'product_conan_manga',
        userId: 'user_demo_renter2',
        userName: 'Pham Thi D',
        rating: 5,
        comment: 'Great manga collection! Perfect condition and very entertaining.',
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        productId: 'product_umbrella_official',
        userId: 'user_demo_renter3',
        userName: 'Hoang Van E',
        rating: 5,
        comment: 'Premium quality umbrella. Very sturdy and professional looking.',
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      }
    ];

    // Add reviews to Firestore
    for (const review of reviews) {
      await db.collection('reviews').add(review);
      console.log(`✅ Created review for product: ${review.productId}`);
    }

    console.log('\n🎉 PingGo rental platform data setup complete!');
    console.log('\n📊 Database Summary:');
    console.log(`   • ${hublockers.length} Hublockers created`);
    console.log(`   • ${products.length} Products created`);
    console.log(`   • ${users.length} Users created`);
    console.log(`   • ${reviews.length} Reviews created`);
    console.log('\n🔗 Access your data:');
    console.log('   • Firebase Console: https://console.firebase.google.com/');
    console.log('   • Project: pinggo-351c6');
    console.log('   • Firestore Database: https://console.firebase.google.com/project/pinggo-351c6/firestore');
    console.log('\n📱 Next steps:');
    console.log('   1. Check your Firestore console to view the data');
    console.log('   2. Update your Flutter app to use these collections');
    console.log('   3. Test the rental functionality with real data');

  } catch (error) {
    console.error('❌ Error setting up PingGo data:', error);
  }
}

// Run the setup
setupPingGoData();
