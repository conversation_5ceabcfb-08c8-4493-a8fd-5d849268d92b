import 'package:flutter/material.dart';
import 'package:lockerhub/models/inventory_item.dart';
import 'package:lockerhub/screens/cancel_rental_screen.dart';
import 'package:lockerhub/screens/return_product_screen.dart';

class InventoryProductDetailScreen extends StatefulWidget {
  final InventoryItem item;
  final bool isDarkMode;

  const InventoryProductDetailScreen({
    super.key,
    required this.item,
    this.isDarkMode = false,
  });

  @override
  State<InventoryProductDetailScreen> createState() => _InventoryProductDetailScreenState();
}

class _InventoryProductDetailScreenState extends State<InventoryProductDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Product Details',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Container(
              width: double.infinity,
              height: 200,
              decoration: BoxDecoration(
                color: const Color(0xFF708871).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.inventory,
                color: Color(0xFF708871),
                size: 80,
              ),
            ),

            const SizedBox(height: 24),

            // Product Name and Rating
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    widget.item.name,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: widget.isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Row(
                  children: [
                    Text(
                      widget.item.rating.toString(),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: widget.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.star,
                      color: Colors.orange,
                      size: 20,
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Status Badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getStatusColor(widget.item.status),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                widget.item.statusDisplayText,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Owner/Renter Information
            if (widget.item.status == InventoryStatus.rented && widget.item.ownerName != null)
              _buildInfoSection(
                'Owner Information',
                [
                  _buildInfoRow('Name', widget.item.ownerName!),
                  if (widget.item.ownerPhone != null)
                    _buildInfoRow('Phone', widget.item.ownerPhone!),
                ],
              ),

            if (widget.item.status == InventoryStatus.delivery && widget.item.renterName != null)
              _buildInfoSection(
                'Renter Information',
                [
                  _buildInfoRow('Name', widget.item.renterName!),
                  if (widget.item.renterPhone != null)
                    _buildInfoRow('Phone', widget.item.renterPhone!),
                ],
              ),

            // Due Date Information
            if (widget.item.dueDate != null)
              _buildInfoSection(
                'Rental Information',
                [
                  _buildInfoRow('Due Date', _formatDate(widget.item.dueDate!)),
                  if (widget.item.daysRemaining != null)
                    _buildInfoRow(
                      'Days Remaining',
                      '${widget.item.daysRemaining} days',
                      isHighlight: widget.item.daysRemaining! <= 3,
                    ),
                  _buildInfoRow('Rental Period', widget.item.rentalPeriodDisplayText),
                  _buildInfoRow('Price', '${widget.item.price.toStringAsFixed(0)} VND'),
                ],
              ),

            // Delivery Information
            if (widget.item.status == InventoryStatus.delivery && widget.item.deliveryDate != null)
              _buildInfoSection(
                'Delivery Information',
                [
                  _buildInfoRow('Expected Delivery', _formatDate(widget.item.deliveryDate!)),
                  _buildInfoRow('Hublocker', widget.item.hublockerName),
                  _buildInfoRow('Address', widget.item.hublockerAddress),
                  if (widget.item.lockerNumber != null)
                    _buildInfoRow('Locker Number', widget.item.lockerNumber!),
                ],
              ),

            // Location Information
            _buildInfoSection(
              'Location',
              [
                _buildInfoRow('Hublocker', widget.item.hublockerName),
                _buildInfoRow('Address', widget.item.hublockerAddress),
                if (widget.item.lockerNumber != null)
                  _buildInfoRow('Locker Number', widget.item.lockerNumber!),
              ],
            ),

            // Description
            _buildInfoSection(
              'Description',
              [
                Text(
                  widget.item.description,
                  style: TextStyle(
                    fontSize: 14,
                    color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                    height: 1.5,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Review Section
            _buildReviewSection(),

            const SizedBox(height: 24),

            // Action Buttons
            _buildActionButtons(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: widget.isDarkMode 
              ? Colors.white.withValues(alpha: 0.1) 
              : Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isHighlight = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: isHighlight 
                  ? Colors.red.shade600 
                  : (widget.isDarkMode ? Colors.white : Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: widget.isDarkMode 
              ? Colors.white.withValues(alpha: 0.1) 
              : Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Review',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'What do you feel about our product?',
            style: TextStyle(
              fontSize: 14,
              color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Rating',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: List.generate(5, (index) {
              return Icon(
                Icons.star_outline,
                color: Colors.grey.shade400,
                size: 32,
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Return Item / Cancel Lease / Cancel Rent Button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: ElevatedButton(
            onPressed: _handlePrimaryAction,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF708871),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              _getPrimaryActionText(),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        // Cancel Button (for delivery and lease items)
        if (widget.item.status == InventoryStatus.delivery || 
            widget.item.status == InventoryStatus.lease) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: OutlinedButton(
              onPressed: _handleCancelAction,
              style: OutlinedButton.styleFrom(
                foregroundColor: const Color(0xFF708871),
                side: const BorderSide(color: Color(0xFF708871)),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                widget.item.status == InventoryStatus.delivery ? 'Cancel Rent' : 'Cancel Lease',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  String _getPrimaryActionText() {
    switch (widget.item.status) {
      case InventoryStatus.rented:
        return 'Return Item';
      case InventoryStatus.lease:
        return 'Cancel Lease';
      case InventoryStatus.delivery:
        return 'Cancel Rent';
    }
  }

  void _handlePrimaryAction() {
    if (widget.item.status == InventoryStatus.rented) {
      _navigateToReturnProduct();
    } else {
      _handleCancelAction();
    }
  }

  void _handleCancelAction() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CancelRentalScreen(
          item: widget.item,
          isDarkMode: widget.isDarkMode,
        ),
      ),
    );
  }

  void _navigateToReturnProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ReturnProductScreen(
          item: widget.item,
          isDarkMode: widget.isDarkMode,
        ),
      ),
    );
  }

  Color _getStatusColor(InventoryStatus status) {
    switch (status) {
      case InventoryStatus.rented:
        return Colors.red.shade400;
      case InventoryStatus.lease:
        return Colors.teal.shade400;
      case InventoryStatus.delivery:
        return Colors.orange.shade400;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
