// Simple script to add data to Firestore using Firebase CLI
const { initializeApp } = require('firebase/app');
const { getFirestore, collection, doc, setDoc, addDoc } = require('firebase/firestore');

// Your Firebase config from firebase_options.dart
const firebaseConfig = {
  apiKey: 'AIzaSyAU3FRDX8Ubgb06lJyLqYOEMTwnHaywT6g',
  authDomain: 'pinggo-351c6.firebaseapp.com',
  projectId: 'pinggo-351c6',
  storageBucket: 'pinggo-351c6.firebasestorage.app',
  messagingSenderId: '41313207774',
  appId: '1:41313207774:web:71d8fbd8c04e504924494f'
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function addAllData() {
  try {
    console.log('🚀 Starting to add PingGo data to Firestore...');

    // Add Hublockers
    const hublockers = [
      {
        id: 'hublock<PERSON>_bachkhoa',
        name: '<PERSON><PERSON><PERSON><PERSON> Bach Khoa',
        address: 'Bach Khoa University, Hanoi',
        city: 'Hanoi',
        district: 'Hai Ba Trung',
        coordinates: { lat: 21.0058, lng: 105.8431 },
        imageUrl: 'lib/images/locker/HubLockBachKhoa.jpg',
        isActive: true,
        capacity: 50,
        availableSlots: 35,
        operatingHours: '6:00 AM - 10:00 PM',
        contactPhone: '+84 24 3868 3008',
        rating: 4.5
      },
      {
        id: 'hublocker_vanhoa',
        name: 'HubLock Van Hoa',
        address: 'Van Hoa Cultural Center, Hanoi',
        city: 'Hanoi',
        district: 'Dong Da',
        coordinates: { lat: 21.0245, lng: 105.8412 },
        imageUrl: 'lib/images/locker/HubLockVanHoa.jpg',
        isActive: true,
        capacity: 40,
        availableSlots: 28,
        operatingHours: '7:00 AM - 9:00 PM',
        contactPhone: '+84 24 3736 2663',
        rating: 4.3
      },
      {
        id: 'hublocker_ilogic',
        name: 'iLogic Smart Locker',
        address: 'iLogic Building, Hanoi',
        city: 'Hanoi',
        district: 'Cau Giay',
        coordinates: { lat: 21.0285, lng: 105.7774 },
        imageUrl: 'lib/images/locker/iLogic.jpg',
        isActive: true,
        capacity: 60,
        availableSlots: 42,
        operatingHours: '24/7',
        contactPhone: '+84 24 3755 6677',
        rating: 4.7
      },
      {
        id: 'hublocker_ilogic_goldenview',
        name: 'iLogic Golden View',
        address: 'Golden View Tower, Hanoi',
        city: 'Hanoi',
        district: 'Nam Tu Liem',
        coordinates: { lat: 21.0378, lng: 105.7804 },
        imageUrl: 'lib/images/locker/iLogicBoxGoldenView.jpg',
        isActive: true,
        capacity: 45,
        availableSlots: 30,
        operatingHours: '6:00 AM - 11:00 PM',
        contactPhone: '+84 24 3755 6688',
        rating: 4.4
      }
    ];

    console.log('📍 Adding hublockers...');
    for (const hublocker of hublockers) {
      const { id, ...data } = hublocker;
      await setDoc(doc(db, 'hublockers', id), data);
      console.log(`✅ Added hublocker: ${hublocker.name}`);
    }

    // Add Products
    const products = [
      {
        id: 'product_ao_mua_agribank',
        name: 'Agribank Rain Jacket',
        description: 'High-quality waterproof rain jacket from Agribank. Perfect for rainy days and outdoor activities.',
        category: 'Clothing',
        subcategory: 'Outerwear',
        images: ['lib/images/product/AoMuaAgribank.jpg'],
        pricing: { daily: 15000, monthly: 300000, quarterly: 800000, yearly: 2500000 },
        ownerId: 'user_demo_owner1',
        ownerName: 'Nguyen Van A',
        ownerPhone: '+84 987 654 321',
        hublocker: 'hublocker_bachkhoa',
        hublockerId: 'hublocker_bachkhoa',
        tags: ['waterproof', 'outdoor', 'rain', 'jacket', 'agribank'],
        isAvailable: true,
        condition: 'excellent',
        size: 'L',
        color: 'Blue',
        brand: 'Agribank',
        rating: 4.6,
        reviewCount: 12,
        rentCount: 25
      },
      {
        id: 'product_calculus_textbook',
        name: 'Calculus 1 Textbook',
        description: 'Essential Calculus 1 textbook for university students. Comprehensive coverage of differential and integral calculus.',
        category: 'Education',
        subcategory: 'Textbooks',
        images: ['lib/images/product/Calculus1.jpg'],
        pricing: { daily: 8000, monthly: 150000, quarterly: 400000, yearly: 1200000 },
        ownerId: 'user_demo_owner2',
        ownerName: 'Tran Thi B',
        ownerPhone: '+84 912 345 678',
        hublocker: 'hublocker_vanhoa',
        hublockerId: 'hublocker_vanhoa',
        tags: ['textbook', 'calculus', 'math', 'university', 'education'],
        isAvailable: true,
        condition: 'good',
        subject: 'Mathematics',
        level: 'University',
        edition: '10th Edition',
        rating: 4.4,
        reviewCount: 8,
        rentCount: 18
      },
      {
        id: 'product_conan_manga',
        name: 'Detective Conan Manga Collection',
        description: 'Popular Detective Conan manga series. Great for entertainment and Japanese language learning.',
        category: 'Entertainment',
        subcategory: 'Books & Manga',
        images: ['lib/images/product/Conan.jpg'],
        pricing: { daily: 5000, monthly: 100000, quarterly: 250000, yearly: 800000 },
        ownerId: 'user_demo_owner3',
        ownerName: 'Le Van C',
        ownerPhone: '+84 901 234 567',
        hublocker: 'hublocker_ilogic',
        hublockerId: 'hublocker_ilogic',
        tags: ['manga', 'detective', 'conan', 'entertainment', 'japanese'],
        isAvailable: true,
        condition: 'excellent',
        language: 'Vietnamese',
        genre: 'Mystery/Detective',
        volumes: '1-20',
        rating: 4.8,
        reviewCount: 15,
        rentCount: 32
      },
      {
        id: 'product_electric_plug',
        name: 'Universal Electric Plug Adapter',
        description: 'Multi-purpose electric plug adapter suitable for various devices. Essential for travelers and students.',
        category: 'Electronics',
        subcategory: 'Accessories',
        images: ['lib/images/product/ElectricPlug.jpg'],
        pricing: { daily: 3000, monthly: 60000, quarterly: 150000, yearly: 500000 },
        ownerId: 'user_demo_owner4',
        ownerName: 'Pham Van D',
        ownerPhone: '+84 923 456 789',
        hublocker: 'hublocker_ilogic_goldenview',
        hublockerId: 'hublocker_ilogic_goldenview',
        tags: ['electronics', 'adapter', 'plug', 'universal', 'travel'],
        isAvailable: true,
        condition: 'excellent',
        type: 'Universal Adapter',
        compatibility: 'Multiple devices',
        voltage: '100-240V',
        rating: 4.5,
        reviewCount: 20,
        rentCount: 45
      },
      {
        id: 'product_hust_uniform',
        name: 'HUST University Uniform',
        description: 'Official HUST (Hanoi University of Science and Technology) uniform. Perfect for university events and ceremonies.',
        category: 'Clothing',
        subcategory: 'Uniforms',
        images: ['lib/images/product/HUSTuniform.jpg', 'lib/images/product/HUSTuniformAfter.jpg'],
        pricing: { daily: 20000, monthly: 400000, quarterly: 1000000, yearly: 3000000 },
        ownerId: 'user_demo_owner5',
        ownerName: 'Hoang Thi E',
        ownerPhone: '+84 934 567 890',
        hublocker: 'hublocker_bachkhoa',
        hublockerId: 'hublocker_bachkhoa',
        tags: ['uniform', 'hust', 'university', 'formal', 'ceremony'],
        isAvailable: true,
        condition: 'good',
        size: 'M',
        university: 'HUST',
        type: 'Official Uniform',
        rating: 4.3,
        reviewCount: 7,
        rentCount: 12
      },
      {
        id: 'product_umbrella_black',
        name: 'Black Umbrella',
        description: 'Sturdy black umbrella perfect for rainy weather. Compact and easy to carry.',
        category: 'Accessories',
        subcategory: 'Weather Protection',
        images: ['lib/images/product/Umbrella_black.jpg', 'lib/images/product/Umbrella.jpg'],
        pricing: { daily: 5000, monthly: 80000, quarterly: 200000, yearly: 600000 },
        ownerId: 'user_demo_owner6',
        ownerName: 'Vu Van F',
        ownerPhone: '+84 945 678 901',
        hublocker: 'hublocker_vanhoa',
        hublockerId: 'hublocker_vanhoa',
        tags: ['umbrella', 'rain', 'weather', 'protection', 'compact'],
        isAvailable: true,
        condition: 'excellent',
        color: 'Black',
        size: 'Standard',
        material: 'Polyester',
        rating: 4.7,
        reviewCount: 25,
        rentCount: 58
      },
      {
        id: 'product_umbrella_official',
        name: 'Official Premium Umbrella',
        description: 'High-quality premium umbrella with official branding. Durable and stylish for professional use.',
        category: 'Accessories',
        subcategory: 'Weather Protection',
        images: ['lib/images/product/Umbrella_official.png'],
        pricing: { daily: 8000, monthly: 120000, quarterly: 300000, yearly: 900000 },
        ownerId: 'user_demo_owner7',
        ownerName: 'Dao Thi G',
        ownerPhone: '+84 956 789 012',
        hublocker: 'hublocker_ilogic',
        hublockerId: 'hublocker_ilogic',
        tags: ['umbrella', 'premium', 'official', 'professional', 'durable'],
        isAvailable: true,
        condition: 'excellent',
        type: 'Premium',
        brand: 'Official',
        warranty: '1 year',
        rating: 4.9,
        reviewCount: 18,
        rentCount: 35
      }
    ];

    console.log('🛍️ Adding products...');
    for (const product of products) {
      const { id, ...data } = product;
      await setDoc(doc(db, 'products', id), data);
      console.log(`✅ Added product: ${product.name}`);
    }

    // Add Users
    const users = [
      {
        id: 'user_demo_owner1',
        firstName: 'Nguyen Van',
        lastName: 'A',
        email: '<EMAIL>',
        phone: '+84 987 654 321',
        address: 'Hanoi, Vietnam',
        isVerified: true,
        rating: 4.8,
        totalRentals: 25,
        totalEarnings: 2500000
      },
      {
        id: 'user_demo_owner2',
        firstName: 'Tran Thi',
        lastName: 'B',
        email: '<EMAIL>',
        phone: '+84 912 345 678',
        address: 'Hanoi, Vietnam',
        isVerified: true,
        rating: 4.6,
        totalRentals: 18,
        totalEarnings: 1800000
      },
      {
        id: 'user_demo_owner3',
        firstName: 'Le Van',
        lastName: 'C',
        email: '<EMAIL>',
        phone: '+84 901 234 567',
        address: 'Hanoi, Vietnam',
        isVerified: true,
        rating: 4.9,
        totalRentals: 32,
        totalEarnings: 3200000
      },
      {
        id: 'user_demo_owner4',
        firstName: 'Pham Van',
        lastName: 'D',
        email: '<EMAIL>',
        phone: '+84 923 456 789',
        address: 'Hanoi, Vietnam',
        isVerified: true,
        rating: 4.7,
        totalRentals: 45,
        totalEarnings: 4500000
      },
      {
        id: 'user_demo_owner5',
        firstName: 'Hoang Thi',
        lastName: 'E',
        email: '<EMAIL>',
        phone: '+84 934 567 890',
        address: 'Hanoi, Vietnam',
        isVerified: true,
        rating: 4.5,
        totalRentals: 12,
        totalEarnings: 1200000
      },
      {
        id: 'user_demo_owner6',
        firstName: 'Vu Van',
        lastName: 'F',
        email: '<EMAIL>',
        phone: '+84 945 678 901',
        address: 'Hanoi, Vietnam',
        isVerified: true,
        rating: 4.8,
        totalRentals: 58,
        totalEarnings: 5800000
      },
      {
        id: 'user_demo_owner7',
        firstName: 'Dao Thi',
        lastName: 'G',
        email: '<EMAIL>',
        phone: '+84 956 789 012',
        address: 'Hanoi, Vietnam',
        isVerified: true,
        rating: 4.9,
        totalRentals: 35,
        totalEarnings: 3500000
      }
    ];

    console.log('👥 Adding users...');
    for (const user of users) {
      const { id, ...data } = user;
      await setDoc(doc(db, 'users', id), data);
      console.log(`✅ Added user: ${user.firstName} ${user.lastName}`);
    }

    // Add Sample Reviews
    const reviews = [
      {
        productId: 'product_ao_mua_agribank',
        userId: 'user_demo_renter1',
        userName: 'Le Van C',
        rating: 5,
        comment: 'Excellent rain jacket! Kept me completely dry during heavy rain.',
        helpful: 8,
        createdAt: new Date()
      },
      {
        productId: 'product_conan_manga',
        userId: 'user_demo_renter2',
        userName: 'Pham Thi D',
        rating: 5,
        comment: 'Great manga collection! Perfect condition and very entertaining.',
        helpful: 12,
        createdAt: new Date()
      },
      {
        productId: 'product_umbrella_official',
        userId: 'user_demo_renter3',
        userName: 'Hoang Van E',
        rating: 5,
        comment: 'Premium quality umbrella. Very sturdy and professional looking.',
        helpful: 6,
        createdAt: new Date()
      }
    ];

    console.log('⭐ Adding reviews...');
    for (const review of reviews) {
      await addDoc(collection(db, 'reviews'), review);
      console.log(`✅ Added review for: ${review.productId}`);
    }

    console.log('\n🎉 All PingGo data added successfully!');
    console.log('\n📊 Database Summary:');
    console.log(`   • ${hublockers.length} Hublockers created`);
    console.log(`   • ${products.length} Products created`);
    console.log(`   • ${users.length} Users created`);
    console.log(`   • ${reviews.length} Reviews created`);
    console.log('\n🔗 Access your data:');
    console.log('   • Firestore Console: https://console.firebase.google.com/project/pinggo-351c6/firestore');
    console.log('\n🚀 Your PingGo rental platform database is ready!');

  } catch (error) {
    console.error('❌ Error adding data:', error);
  }
}

addAllData();
