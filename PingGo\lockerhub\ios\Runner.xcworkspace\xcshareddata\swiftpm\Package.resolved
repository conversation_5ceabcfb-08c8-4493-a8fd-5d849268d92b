{"pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bfc0b6f81adc06ce5121eb23f628473638d67c5c", "version": "1.2022062300.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "e700a8f40c87c31cab7984875fcc1225d96b25bf", "version": "10.11.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "62e3a0c09a75e2637f5300d46f05a59313f1c286", "version": "10.11.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "98a00258d4518b7521253a70b7f70bb76d2120fe", "version": "9.2.4"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "6039a55afc03aac62e898aea4acdbe20d383978c", "version": "7.11.2"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "f1b366129d1125be7db83247e003fc333104b569", "version": "1.50.2"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "d415594121c9e8a4f9d79cecee0965cf35e74dbd", "version": "3.1.1"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "0706abcc6b0bd9cedfbb015ba840e4a780b5159b", "version": "1.22.2"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "819d0a2173aff699fb8c364b6fb906f7cdb1a692", "version": "2.30909.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "ec957ccddbcc710ccc64c9dcbd4c7006fcf8b73a", "version": "2.2.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "f25867a208f459d3c5a06935dceb9083b11cd539", "version": "1.22.0"}}], "version": 2}