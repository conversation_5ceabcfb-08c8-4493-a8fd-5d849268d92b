# LockerHub Backend Setup Guide

## 1. Enable Google Cloud APIs

```bash
# Enable required APIs
gcloud services enable cloudfunctions.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable firebase.googleapis.com
```

## 2. Install Firebase CLI

```bash
# Install Firebase CLI globally
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in your project
cd LockerHub
firebase init
```

## 3. Select Firebase Features

When running `firebase init`, select:
- ✅ Firestore: Configure security rules and indexes
- ✅ Functions: Configure a Cloud Functions directory
- ✅ Hosting: Configure files for Firebase Hosting

## 4. Project Structure After Init

```
LockerHub/
├── functions/
│   ├── index.js          # Cloud Functions code
│   ├── package.json      # Node.js dependencies
│   └── .gitignore
├── firestore.rules       # Database security rules
├── firestore.indexes.json
├── firebase.json         # Firebase configuration
└── lockerhub/           # Flutter app
```

## 5. Install Function Dependencies

```bash
cd functions
npm install firebase-functions firebase-admin
```
