import 'package:flutter/material.dart';
import 'package:lockerhub/models/rental_request.dart';
import 'package:lockerhub/services/notification_service.dart';

class RentalRequestDetailScreen extends StatefulWidget {
  final RentalRequest request;
  final bool isDarkMode;

  const RentalRequestDetailScreen({
    super.key,
    required this.request,
    this.isDarkMode = false,
  });

  @override
  State<RentalRequestDetailScreen> createState() => _RentalRequestDetailScreenState();
}

class _RentalRequestDetailScreenState extends State<RentalRequestDetailScreen> {
  final NotificationService _notificationService = NotificationService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Rental Request',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Request Status
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getStatusColor(widget.request.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getStatusColor(widget.request.status),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getStatusIcon(widget.request.status),
                    color: _getStatusColor(widget.request.status),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Status: ${widget.request.statusDisplayText}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: _getStatusColor(widget.request.status),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Product Information
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: widget.isDarkMode 
                      ? Colors.white.withOpacity(0.1) 
                      : Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: const Color(0xFF708871).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.inventory,
                      color: Color(0xFF708871),
                      size: 40,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.request.productName,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: widget.isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${widget.request.rentalPrice.toStringAsFixed(0)} VND/${widget.request.rentalPeriod}',
                          style: TextStyle(
                            fontSize: 14,
                            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Requested ${widget.request.timeAgo}',
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Renter Information
            _buildInfoSection(
              'Renter Information',
              [
                _buildInfoRow('Name', widget.request.renterName),
                _buildInfoRow('Phone', widget.request.renterPhone),
                _buildInfoRow('Email', widget.request.renterEmail),
              ],
            ),

            // Rental Details
            _buildInfoSection(
              'Rental Details',
              [
                _buildInfoRow('Rental Period', widget.request.rentalPeriod),
                _buildInfoRow('Price', '${widget.request.rentalPrice.toStringAsFixed(0)} VND'),
                if (widget.request.startDate != null)
                  _buildInfoRow('Start Date', _formatDate(widget.request.startDate!)),
                if (widget.request.endDate != null)
                  _buildInfoRow('End Date', _formatDate(widget.request.endDate!)),
              ],
            ),

            // Pickup Location
            _buildInfoSection(
              'Pickup Location',
              [
                _buildInfoRow('Hublocker', widget.request.hublockerName),
                _buildInfoRow('Address', widget.request.hublockerAddress),
                if (widget.request.lockerNumber != null)
                  _buildInfoRow('Locker Number', widget.request.lockerNumber!),
              ],
            ),

            // QR Code and Pin (if confirmed)
            if (widget.request.status == RentalRequestStatus.confirmed) ...[
              _buildInfoSection(
                'Access Information',
                [
                  if (widget.request.qrCode != null)
                    _buildQRCodeSection(),
                  if (widget.request.accessPin != null)
                    _buildInfoRow('Access Pin', widget.request.accessPin!),
                ],
              ),
            ],

            const SizedBox(height: 24),

            // Action Buttons (only for pending requests)
            if (widget.request.status == RentalRequestStatus.pending) ...[
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _rejectRequest,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red.shade600,
                        side: BorderSide(color: Colors.red.shade600),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        'Reject',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _confirmRequest,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF708871),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text(
                        'Confirm',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: widget.isDarkMode 
              ? Colors.white.withOpacity(0.1) 
              : Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: widget.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQRCodeSection() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.isDarkMode ? Colors.grey.shade700 : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Icon(
                Icons.qr_code,
                size: 80,
                color: Colors.black,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'QR Code for Locker Access',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: widget.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            widget.request.qrCode ?? '',
            style: TextStyle(
              fontSize: 12,
              color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(RentalRequestStatus status) {
    switch (status) {
      case RentalRequestStatus.pending:
        return Colors.orange;
      case RentalRequestStatus.confirmed:
        return Colors.green;
      case RentalRequestStatus.rejected:
        return Colors.red;
      case RentalRequestStatus.completed:
        return Colors.blue;
    }
  }

  IconData _getStatusIcon(RentalRequestStatus status) {
    switch (status) {
      case RentalRequestStatus.pending:
        return Icons.schedule;
      case RentalRequestStatus.confirmed:
        return Icons.check_circle;
      case RentalRequestStatus.rejected:
        return Icons.cancel;
      case RentalRequestStatus.completed:
        return Icons.done_all;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _rejectRequest() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        title: Text(
          'Reject Request',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        content: Text(
          'Are you sure you want to reject this rental request?',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processRejection();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _confirmRequest() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        title: Text(
          'Confirm Request',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        content: Text(
          'Are you sure you want to confirm this rental request? A QR code and access pin will be generated for the renter.',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processConfirmation();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF708871),
            ),
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _processRejection() {
    // Send notification to renter
    _notificationService.addNotification(AppNotification(
      id: 'rejection_${widget.request.id}_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Rental Request Rejected',
      message: 'Your rental request for "${widget.request.productName}" has been rejected by the owner.',
      type: NotificationType.statusChange,
      timestamp: DateTime.now(),
      relatedItemId: widget.request.productId,
    ));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Rental request rejected. Renter has been notified.'),
        backgroundColor: Colors.red,
      ),
    );

    Navigator.pop(context);
  }

  void _processConfirmation() {
    // Generate QR code and access pin for owner to place product
    final qrCode = widget.request.generateQRCode();
    final accessPin = widget.request.generateAccessPin();

    // Send initial confirmation notification to renter
    _notificationService.addLockerRentalConfirmation(
      productName: widget.request.productName,
      hublockerName: widget.request.hublockerName,
      hublockerAddress: widget.request.hublockerAddress,
      relatedItemId: widget.request.productId,
    );

    // Show dialog to owner with QR code for placing product
    _showOwnerQRCodeDialog(qrCode, accessPin);
  }

  void _showOwnerQRCodeDialog(String qrCode, String accessPin) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        title: Text(
          'Place Product in Locker',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Use this QR code or PIN to access the locker and place your product:',
              style: TextStyle(
                color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 20),

            // QR Code Display
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300, width: 2),
              ),
              child: const Center(
                child: Icon(
                  Icons.qr_code,
                  size: 100,
                  color: Colors.black,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // QR Code Text
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.isDarkMode ? Colors.grey.shade700 : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                qrCode,
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                  color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Access PIN
            Text(
              'Access PIN: $accessPin',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: widget.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Go back to previous screen
            },
            child: Text(
              'Cancel',
              style: TextStyle(
                color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              _simulateProductPlacement();
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Go back to previous screen
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF708871),
            ),
            child: const Text(
              'Product Placed',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  void _simulateProductPlacement() {
    // Simulate owner placing product in locker
    // Send notification to renter that product is ready for pickup
    _notificationService.addProductPlacementNotification(
      productName: widget.request.productName,
      hublockerName: widget.request.hublockerName,
      hublockerAddress: widget.request.hublockerAddress,
      relatedItemId: widget.request.productId,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Product placed successfully! Renter has been notified with pickup QR code.'),
        backgroundColor: Color(0xFF708871),
        duration: Duration(seconds: 3),
      ),
    );
  }
}
