import 'package:flutter/material.dart';
import 'package:lockerhub/models/inventory_item.dart';
import 'package:lockerhub/models/product_damage.dart';
import 'package:lockerhub/screens/damage_assessment_screen.dart';

class AIAnalysisScreen extends StatefulWidget {
  final InventoryItem item;
  final ProductCategory category;
  final List<String> capturedPhotos;
  final bool isDarkMode;

  const AIAnalysisScreen({
    super.key,
    required this.item,
    required this.category,
    required this.capturedPhotos,
    this.isDarkMode = false,
  });

  @override
  State<AIAnalysisScreen> createState() => _AIAnalysisScreenState();
}

class _AIAnalysisScreenState extends State<AIAnalysisScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  bool isAnalyzing = true;
  AIAnalysisResult? analysisResult;
  
  final List<String> analysisSteps = [
    'Loading captured images...',
    'Comparing with original product photos...',
    'Detecting surface damage...',
    'Analyzing structural integrity...',
    'Calculating similarity score...',
    'Generating damage assessment...',
  ];

  int currentStepIndex = 0;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _startAnalysis();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _startAnalysis() async {
    _pulseController.repeat(reverse: true);
    _progressController.forward();

    // Simulate analysis steps
    for (int i = 0; i < analysisSteps.length; i++) {
      await Future.delayed(const Duration(milliseconds: 1000));
      if (mounted) {
        setState(() {
          currentStepIndex = i;
        });
      }
    }

    // Generate AI analysis result
    await Future.delayed(const Duration(milliseconds: 500));
    
    if (mounted) {
      final result = AIAnalysisResult.simulateAnalysis(
        ['original_1', 'original_2'], // Simulated original photos
        widget.capturedPhotos,
      );

      setState(() {
        analysisResult = result;
        isAnalyzing = false;
      });

      _pulseController.stop();
      
      // Auto-proceed to results after a brief delay
      await Future.delayed(const Duration(milliseconds: 1500));
      if (mounted) {
        _proceedToResults();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              // Header
              Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.arrow_back,
                      color: widget.isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      'AI Analysis',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: widget.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                  const SizedBox(width: 48),
                ],
              ),

              const SizedBox(height: 40),

              // Analysis Animation
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // AI Brain Animation
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: isAnalyzing ? _pulseAnimation.value : 1.0,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: const Color(0xFF708871).withOpacity(0.2),
                              borderRadius: BorderRadius.circular(60),
                              border: Border.all(
                                color: const Color(0xFF708871),
                                width: 2,
                              ),
                            ),
                            child: Icon(
                              isAnalyzing ? Icons.psychology : Icons.check_circle,
                              color: const Color(0xFF708871),
                              size: 60,
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 32),

                    // Status Text
                    Text(
                      isAnalyzing ? 'Analyzing Product Condition...' : 'Analysis Complete!',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: widget.isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),

                    const SizedBox(height: 16),

                    if (isAnalyzing) ...[
                      // Progress Bar
                      Container(
                        width: double.infinity,
                        height: 8,
                        decoration: BoxDecoration(
                          color: widget.isDarkMode 
                            ? Colors.grey.shade800 
                            : Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: AnimatedBuilder(
                          animation: _progressAnimation,
                          builder: (context, child) {
                            return FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: _progressAnimation.value,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: const Color(0xFF708871),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            );
                          },
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Current Step
                      Text(
                        currentStepIndex < analysisSteps.length 
                          ? analysisSteps[currentStepIndex]
                          : 'Finalizing results...',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: widget.isDarkMode 
                            ? Colors.grey.shade300 
                            : Colors.grey.shade600,
                        ),
                      ),

                      const SizedBox(height: 32),

                      // Analysis Steps
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: widget.isDarkMode 
                            ? Colors.grey.shade800 
                            : Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: widget.isDarkMode 
                                ? Colors.white.withOpacity(0.1) 
                                : Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Processing Steps:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: widget.isDarkMode ? Colors.white : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 12),
                            ...analysisSteps.asMap().entries.map((entry) {
                              final index = entry.key;
                              final step = entry.value;
                              final isCompleted = index <= currentStepIndex;
                              final isCurrent = index == currentStepIndex;

                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 20,
                                      height: 20,
                                      decoration: BoxDecoration(
                                        color: isCompleted 
                                          ? const Color(0xFF708871) 
                                          : Colors.transparent,
                                        border: Border.all(
                                          color: isCompleted 
                                            ? const Color(0xFF708871) 
                                            : Colors.grey.shade400,
                                          width: 2,
                                        ),
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                      child: isCompleted
                                        ? const Icon(
                                            Icons.check,
                                            color: Colors.white,
                                            size: 12,
                                          )
                                        : null,
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        step,
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: isCurrent
                                            ? const Color(0xFF708871)
                                            : (widget.isDarkMode 
                                                ? Colors.grey.shade300 
                                                : Colors.grey.shade600),
                                          fontWeight: isCurrent 
                                            ? FontWeight.w600 
                                            : FontWeight.normal,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                          ],
                        ),
                      ),
                    ] else ...[
                      // Analysis Complete
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: const Color(0xFF708871).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: const Color(0xFF708871).withOpacity(0.3),
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: const Color(0xFF708871),
                              size: 48,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Product analysis completed successfully!',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Proceeding to damage assessment...',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: widget.isDarkMode 
                                  ? Colors.grey.shade300 
                                  : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _proceedToResults() {
    if (analysisResult != null) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => DamageAssessmentScreen(
            item: widget.item,
            category: widget.category,
            analysisResult: analysisResult!,
            isDarkMode: widget.isDarkMode,
          ),
        ),
      );
    }
  }
}
