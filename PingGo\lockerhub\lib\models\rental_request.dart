enum RentalRequestStatus {
  pending,
  confirmed,
  rejected,
  completed,
}

class RentalRequest {
  final String id;
  final String productId;
  final String productName;
  final String productImageUrl;
  final String renterName;
  final String renterPhone;
  final String renterEmail;
  final String renterAvatar;
  final String ownerName;
  final String ownerPhone;
  final String ownerEmail;
  final double rentalPrice;
  final String rentalPeriod;
  final DateTime requestDate;
  final DateTime? startDate;
  final DateTime? endDate;
  final RentalRequestStatus status;
  final String? rejectionReason;
  final String? qrCode;
  final String? accessPin;
  final String hublockerName;
  final String hublockerAddress;
  final String? lockerNumber;

  RentalRequest({
    required this.id,
    required this.productId,
    required this.productName,
    required this.productImageUrl,
    required this.renterName,
    required this.renterPhone,
    required this.renterEmail,
    required this.renterAvatar,
    required this.ownerName,
    required this.ownerPhone,
    required this.ownerEmail,
    required this.rentalPrice,
    required this.rentalPeriod,
    required this.requestDate,
    this.startDate,
    this.endDate,
    this.status = RentalRequestStatus.pending,
    this.rejectionReason,
    this.qrCode,
    this.accessPin,
    required this.hublockerName,
    required this.hublockerAddress,
    this.lockerNumber,
  });

  String get statusDisplayText {
    switch (status) {
      case RentalRequestStatus.pending:
        return 'Pending';
      case RentalRequestStatus.confirmed:
        return 'Confirmed';
      case RentalRequestStatus.rejected:
        return 'Rejected';
      case RentalRequestStatus.completed:
        return 'Completed';
    }
  }

  String get statusColor {
    switch (status) {
      case RentalRequestStatus.pending:
        return '#FFE66D'; // Yellow
      case RentalRequestStatus.confirmed:
        return '#4ECDC4'; // Teal
      case RentalRequestStatus.rejected:
        return '#FF6B6B'; // Red
      case RentalRequestStatus.completed:
        return '#95E1D3'; // Light Green
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(requestDate);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${requestDate.day}/${requestDate.month}/${requestDate.year}';
    }
  }

  RentalRequest copyWith({
    String? id,
    String? productId,
    String? productName,
    String? productImageUrl,
    String? renterName,
    String? renterPhone,
    String? renterEmail,
    String? renterAvatar,
    String? ownerName,
    String? ownerPhone,
    String? ownerEmail,
    double? rentalPrice,
    String? rentalPeriod,
    DateTime? requestDate,
    DateTime? startDate,
    DateTime? endDate,
    RentalRequestStatus? status,
    String? rejectionReason,
    String? qrCode,
    String? accessPin,
    String? hublockerName,
    String? hublockerAddress,
    String? lockerNumber,
  }) {
    return RentalRequest(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      productImageUrl: productImageUrl ?? this.productImageUrl,
      renterName: renterName ?? this.renterName,
      renterPhone: renterPhone ?? this.renterPhone,
      renterEmail: renterEmail ?? this.renterEmail,
      renterAvatar: renterAvatar ?? this.renterAvatar,
      ownerName: ownerName ?? this.ownerName,
      ownerPhone: ownerPhone ?? this.ownerPhone,
      ownerEmail: ownerEmail ?? this.ownerEmail,
      rentalPrice: rentalPrice ?? this.rentalPrice,
      rentalPeriod: rentalPeriod ?? this.rentalPeriod,
      requestDate: requestDate ?? this.requestDate,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      qrCode: qrCode ?? this.qrCode,
      accessPin: accessPin ?? this.accessPin,
      hublockerName: hublockerName ?? this.hublockerName,
      hublockerAddress: hublockerAddress ?? this.hublockerAddress,
      lockerNumber: lockerNumber ?? this.lockerNumber,
    );
  }

  // Generate QR code for locker access
  String generateQRCode() {
    return 'PINGGO_${productId}_${id}_${DateTime.now().millisecondsSinceEpoch}';
  }

  // Generate access pin for locker
  String generateAccessPin() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return (random % 900000 + 100000).toString(); // 6-digit pin
  }
}
