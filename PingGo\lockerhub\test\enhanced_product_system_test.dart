import 'package:flutter_test/flutter_test.dart';
import 'package:lockerhub/models/product.dart';
import 'package:lockerhub/data/sample_products.dart';

void main() {
  group('Enhanced Product System Tests', () {
    test('Product model should have correct properties', () {
      final products = SampleProducts.getAllProducts();
      expect(products.isNotEmpty, true);

      final firstProduct = products.first;
      expect(firstProduct.id.isNotEmpty, true);
      expect(firstProduct.name.isNotEmpty, true);
      expect(firstProduct.description.isNotEmpty, true);
      expect(firstProduct.pricing.isNotEmpty, true);
      expect(firstProduct.category, isA<ProductCategory>());
    });

    test('Product pricing should work correctly', () {
      final products = SampleProducts.getAllProducts();
      final product = products.first;

      // Test pricing retrieval
      final dayPricing = product.getPricing(RentalPeriod.day);
      expect(dayPricing, isNotNull);
      expect(dayPricing!.price, greaterThan(0));

      // Test price range
      expect(product.priceRange.isNotEmpty, true);
      expect(product.priceRange.contains('VND'), true);
    });

    test('Product categories should work correctly', () {
      final products = SampleProducts.getAllProducts();
      
      // Test category filtering
      final electronicsProducts = SampleProducts.getProductsByCategory(ProductCategory.electronics);
      expect(electronicsProducts.isNotEmpty, true);
      
      for (final product in electronicsProducts) {
        expect(product.belongsToCategory(ProductCategory.electronics), true);
      }
    });

    test('Product search should work correctly', () {
      // Test search functionality
      final searchResults = SampleProducts.searchProducts('gaming');
      expect(searchResults.isNotEmpty, true);
      
      for (final product in searchResults) {
        final matchesName = product.name.toLowerCase().contains('gaming');
        final matchesDescription = product.description.toLowerCase().contains('gaming');
        final matchesTags = product.tags.any((tag) => tag.name.toLowerCase().contains('gaming'));
        
        expect(matchesName || matchesDescription || matchesTags, true);
      }
    });

    test('Featured products should be available', () {
      final featuredProducts = SampleProducts.getFeaturedProducts();
      expect(featuredProducts.isNotEmpty, true);
      
      for (final product in featuredProducts) {
        expect(product.isFeatured, true);
      }
    });

    test('Product specifications should be accessible', () {
      final products = SampleProducts.getAllProducts();
      final productWithSpecs = products.firstWhere(
        (p) => p.specifications.isNotEmpty,
        orElse: () => products.first,
      );
      
      if (productWithSpecs.specifications.isNotEmpty) {
        expect(productWithSpecs.specifications, isA<Map<String, dynamic>>());
      }
    });

    test('Product tags should have valid colors', () {
      final products = SampleProducts.getAllProducts();
      
      for (final product in products) {
        for (final tag in product.tags) {
          expect(tag.color.startsWith('#'), true);
          expect(tag.color.length, equals(7)); // #RRGGBB format
          expect(tag.name.isNotEmpty, true);
        }
      }
    });

    test('Product rental periods should be valid', () {
      final products = SampleProducts.getAllProducts();
      
      for (final product in products) {
        expect(product.pricing.isNotEmpty, true);
        
        for (final pricing in product.pricing) {
          expect(pricing.price, greaterThan(0));
          expect(pricing.label.isNotEmpty, true);
          expect(pricing.displayText.isNotEmpty, true);
          expect(pricing.period, isA<RentalPeriod>());
        }
      }
    });

    test('Product availability should be tracked', () {
      final products = SampleProducts.getAllProducts();
      
      for (final product in products) {
        expect(product.availableQuantity, greaterThanOrEqualTo(0));
        expect(product.isAvailable, isA<bool>());
      }
    });

    test('Product reviews should have valid structure', () {
      final products = SampleProducts.getAllProducts();
      final productWithReviews = products.firstWhere(
        (p) => p.reviews.isNotEmpty,
        orElse: () => products.first,
      );
      
      if (productWithReviews.reviews.isNotEmpty) {
        final review = productWithReviews.reviews.first;
        expect(review.userName.isNotEmpty, true);
        expect(review.rating, greaterThanOrEqualTo(0));
        expect(review.rating, lessThanOrEqualTo(5));
        expect(review.comment.isNotEmpty, true);
        expect(review.timeAgo.isNotEmpty, true);
      }
    });

    test('All product categories should have display names', () {
      for (final category in ProductCategory.values) {
        final displayName = SampleProducts.getCategoryDisplayName(category);
        expect(displayName.isNotEmpty, true);
        expect(displayName, isNot(equals(category.name)));
      }
    });

    test('Product copy functionality should work', () {
      final product = SampleProducts.getAllProducts().first;
      final copiedProduct = product.copyWith(name: 'Updated Name');
      
      expect(copiedProduct.name, equals('Updated Name'));
      expect(copiedProduct.id, equals(product.id));
      expect(copiedProduct.description, equals(product.description));
    });
  });
}
