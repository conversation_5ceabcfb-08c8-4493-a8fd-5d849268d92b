import 'package:flutter/foundation.dart';

class CartService extends ChangeNotifier {
  static final CartService _instance = CartService._internal();
  factory CartService() => _instance;
  CartService._internal();

  final List<CartItem> _cartItems = [];

  List<CartItem> get cartItems => List.unmodifiable(_cartItems);
  
  int get itemCount => _cartItems.fold(0, (sum, item) => sum + item.quantity);
  
  double get subtotal => _cartItems.fold(0, (sum, item) => sum + (item.price * item.quantity));
  
  double get lockerFee => 5000.0; // Default locker fee
  
  double get total => subtotal + lockerFee;

  bool get isEmpty => _cartItems.isEmpty;

  // Add item to cart
  void addItem(CartItem item) {
    // Check if item already exists in cart
    final existingIndex = _cartItems.indexWhere((cartItem) => cartItem.id == item.id);
    
    if (existingIndex >= 0) {
      // Update existing item quantity
      _cartItems[existingIndex] = _cartItems[existingIndex].copyWith(
        quantity: _cartItems[existingIndex].quantity + item.quantity,
      );
    } else {
      // Add new item
      _cartItems.add(item);
    }
    
    notifyListeners();
  }

  // Remove item from cart
  void removeItem(String itemId) {
    _cartItems.removeWhere((item) => item.id == itemId);
    notifyListeners();
  }

  // Update item quantity
  void updateQuantity(String itemId, int quantity) {
    final index = _cartItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      if (quantity <= 0) {
        removeItem(itemId);
      } else {
        _cartItems[index] = _cartItems[index].copyWith(quantity: quantity);
        notifyListeners();
      }
    }
  }

  // Update item rental period
  void updateRentalPeriod(String itemId, String rentalPeriod, String rentalPeriodLabel, double newPrice) {
    final index = _cartItems.indexWhere((item) => item.id == itemId);
    if (index >= 0) {
      _cartItems[index] = _cartItems[index].copyWith(
        rentalPeriod: rentalPeriod,
        rentalPeriodLabel: rentalPeriodLabel,
        price: newPrice,
      );
      notifyListeners();
    }
  }

  // Clear all items
  void clearCart() {
    _cartItems.clear();
    notifyListeners();
  }

  // Get item by ID
  CartItem? getItem(String itemId) {
    try {
      return _cartItems.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  // Check if item exists in cart
  bool hasItem(String itemId) {
    return _cartItems.any((item) => item.id == itemId);
  }

  // Get cart items as Map for backward compatibility
  List<Map<String, dynamic>> getCartItemsAsMap() {
    return _cartItems.map((item) => item.toMap()).toList();
  }
}

class CartItem {
  final String id;
  final String name;
  final double price;
  final int quantity;
  final String rentalPeriod;
  final String rentalPeriodLabel;
  final String image;
  final String owner;
  final String location;
  final String? hublocker;

  CartItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    required this.rentalPeriod,
    required this.rentalPeriodLabel,
    required this.image,
    required this.owner,
    required this.location,
    this.hublocker,
  });

  CartItem copyWith({
    String? id,
    String? name,
    double? price,
    int? quantity,
    String? rentalPeriod,
    String? rentalPeriodLabel,
    String? image,
    String? owner,
    String? location,
    String? hublocker,
  }) {
    return CartItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      rentalPeriod: rentalPeriod ?? this.rentalPeriod,
      rentalPeriodLabel: rentalPeriodLabel ?? this.rentalPeriodLabel,
      image: image ?? this.image,
      owner: owner ?? this.owner,
      location: location ?? this.location,
      hublocker: hublocker ?? this.hublocker,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'quantity': quantity,
      'rentalPeriod': rentalPeriod,
      'rentalPeriodLabel': rentalPeriodLabel,
      'image': image,
      'owner': owner,
      'location': location,
      'hublocker': hublocker,
    };
  }

  factory CartItem.fromMap(Map<String, dynamic> map) {
    return CartItem(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      price: (map['price'] ?? 0.0).toDouble(),
      quantity: map['quantity'] ?? 1,
      rentalPeriod: map['rentalPeriod'] ?? 'month',
      rentalPeriodLabel: map['rentalPeriodLabel'] ?? 'Month',
      image: map['image'] ?? '',
      owner: map['owner'] ?? '',
      location: map['location'] ?? '',
      hublocker: map['hublocker'],
    );
  }

  double get totalPrice => price * quantity;
}
