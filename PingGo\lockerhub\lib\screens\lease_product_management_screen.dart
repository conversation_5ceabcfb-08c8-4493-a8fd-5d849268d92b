import 'package:flutter/material.dart';
import 'package:lockerhub/models/inventory_item.dart';
import 'package:lockerhub/screens/edit_product_screen.dart';

class LeaseProductManagementScreen extends StatefulWidget {
  final InventoryItem item;
  final bool isDarkMode;

  const LeaseProductManagementScreen({
    super.key,
    required this.item,
    this.isDarkMode = false,
  });

  @override
  State<LeaseProductManagementScreen> createState() => _LeaseProductManagementScreenState();
}

class _LeaseProductManagementScreenState extends State<LeaseProductManagementScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
      appBar: AppBar(
        backgroundColor: widget.isDarkMode ? Colors.black : const Color(0xFFFEF3E2),
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Manage Product',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Information Card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: widget.isDarkMode 
                      ? Colors.white.withOpacity(0.1) 
                      : Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: const Color(0xFF708871).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.inventory,
                      color: Color(0xFF708871),
                      size: 40,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.item.name,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: widget.isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${widget.item.price.toStringAsFixed(0)} VND/${widget.item.rentalPeriodDisplayText.toLowerCase()}',
                          style: TextStyle(
                            fontSize: 14,
                            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: _getLocationColor(widget.item.productLocation),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                widget.item.productLocationDisplayText,
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Product Status Information
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: widget.isDarkMode 
                      ? Colors.white.withOpacity(0.1) 
                      : Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Product Status',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: widget.isDarkMode ? Colors.white : const Color(0xFF708871),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildStatusRow('Location', widget.item.productLocationDisplayText),
                  _buildStatusRow('Hublocker', widget.item.hublockerName),
                  _buildStatusRow('Address', widget.item.hublockerAddress),
                  if (widget.item.lockerNumber != null)
                    _buildStatusRow('Locker Number', widget.item.lockerNumber!),
                  _buildStatusRow('Rating', '${widget.item.rating}/5.0 (${widget.item.reviewCount} reviews)'),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Management Actions
            Text(
              'Management Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: widget.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
            const SizedBox(height: 16),

            // Edit Product Button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton.icon(
                onPressed: widget.item.isEditable ? _editProduct : null,
                icon: const Icon(Icons.edit),
                label: const Text('Edit Product Information'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF708871),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Update Location Button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton.icon(
                onPressed: _updateLocation,
                icon: const Icon(Icons.location_on),
                label: const Text('Update Location Status'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Remove from Platform Button
            SizedBox(
              width: double.infinity,
              height: 48,
              child: OutlinedButton.icon(
                onPressed: _removeFromPlatform,
                icon: const Icon(Icons.remove_circle_outline),
                label: const Text('Remove from Platform'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red.shade600,
                  side: BorderSide(color: Colors.red.shade600),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: widget.isDarkMode ? Colors.white : Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getLocationColor(ProductLocation location) {
    switch (location) {
      case ProductLocation.inLocker:
        return Colors.teal.shade400;
      case ProductLocation.notDelivered:
        return Colors.red.shade400;
      case ProductLocation.inTransit:
        return Colors.orange.shade400;
    }
  }

  void _editProduct() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditProductScreen(
          item: widget.item,
          isDarkMode: widget.isDarkMode,
        ),
      ),
    );
  }

  void _updateLocation() {
    showModalBottomSheet(
      context: context,
      backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildLocationUpdateSheet(),
    );
  }

  Widget _buildLocationUpdateSheet() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Update Location Status',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: widget.isDarkMode ? Colors.white : Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Select the current location status of your product:',
            style: TextStyle(
              fontSize: 14,
              color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 20),
          
          _buildLocationOption(
            ProductLocation.notDelivered,
            'Not Delivered',
            'Product is still with you',
            Icons.home,
          ),
          _buildLocationOption(
            ProductLocation.inTransit,
            'In Transit',
            'Product is being delivered to hublocker',
            Icons.local_shipping,
          ),
          _buildLocationOption(
            ProductLocation.inLocker,
            'In Locker',
            'Product is available in the hublocker',
            Icons.inventory_2,
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildLocationOption(ProductLocation location, String title, String subtitle, IconData icon) {
    final isSelected = widget.item.productLocation == location;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isSelected 
              ? const Color(0xFF708871) 
              : (widget.isDarkMode ? Colors.grey.shade700 : Colors.grey.shade200),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Icon(
            icon,
            color: isSelected 
              ? Colors.white 
              : (widget.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600),
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            fontSize: 14,
            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade600,
          ),
        ),
        onTap: () {
          Navigator.pop(context);
          _confirmLocationUpdate(location);
        },
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: isSelected 
              ? const Color(0xFF708871) 
              : (widget.isDarkMode ? Colors.grey.shade600 : Colors.grey.shade300),
          ),
        ),
      ),
    );
  }

  void _confirmLocationUpdate(ProductLocation newLocation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        title: Text(
          'Update Location',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        content: Text(
          'Are you sure you want to update the location status to "${_getLocationDisplayText(newLocation)}"?',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processLocationUpdate(newLocation);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF708871),
            ),
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  String _getLocationDisplayText(ProductLocation location) {
    switch (location) {
      case ProductLocation.inLocker:
        return 'In Locker';
      case ProductLocation.notDelivered:
        return 'Not Delivered';
      case ProductLocation.inTransit:
        return 'In Transit';
    }
  }

  void _processLocationUpdate(ProductLocation newLocation) {
    // Update the item location
    setState(() {
      // In a real app, this would update the backend
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Location updated to ${_getLocationDisplayText(newLocation)}'),
        backgroundColor: const Color(0xFF708871),
      ),
    );
  }

  void _removeFromPlatform() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: widget.isDarkMode ? Colors.grey.shade800 : Colors.white,
        title: Text(
          'Remove Product',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.white : Colors.black,
          ),
        ),
        content: Text(
          'Are you sure you want to remove "${widget.item.name}" from the platform? This action cannot be undone.',
          style: TextStyle(
            color: widget.isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _confirmRemoval();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _confirmRemoval() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Product removed from platform successfully'),
        backgroundColor: Colors.red,
      ),
    );

    // Navigate back to inventory
    Navigator.of(context).popUntil((route) => route.isFirst);
  }
}
