enum InventoryStatus {
  rented,
  lease,
  delivery,
}

enum ProductLocation {
  inLocker,
  notDelivered,
  inTransit,
}

enum RentalPeriod {
  day,
  month,
  quarterly,
  year,
}

class InventoryItem {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final double price;
  final RentalPeriod rentalPeriod;
  final InventoryStatus status;
  final DateTime createdDate;
  final DateTime? dueDate;
  final DateTime? deliveryDate;
  final String? ownerName;
  final String? ownerPhone;
  final String? renterName;
  final String? renterPhone;
  final String hublockerName;
  final String hublockerAddress;
  final double rating;
  final int reviewCount;
  final List<Review> reviews;
  final bool canCancel;
  final String? lockerNumber;
  final ProductLocation productLocation;
  final String? qrCode;
  final String? accessPin;
  final bool isEditable;

  InventoryItem({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.price,
    required this.rentalPeriod,
    required this.status,
    required this.createdDate,
    this.dueDate,
    this.deliveryDate,
    this.ownerName,
    this.ownerPhone,
    this.renterName,
    this.renterPhone,
    required this.hublockerName,
    required this.hublockerAddress,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.reviews = const [],
    this.canCancel = true,
    this.lockerNumber,
    this.productLocation = ProductLocation.notDelivered,
    this.qrCode,
    this.accessPin,
    this.isEditable = true,
  });

  // Calculate days remaining until due date
  int? get daysRemaining {
    if (dueDate == null) return null;
    final now = DateTime.now();
    final difference = dueDate!.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  // Check if item is overdue
  bool get isOverdue {
    if (dueDate == null) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  // Get status display text
  String get statusDisplayText {
    switch (status) {
      case InventoryStatus.rented:
        return 'Rented';
      case InventoryStatus.lease:
        return 'Lease';
      case InventoryStatus.delivery:
        return 'Delivery';
    }
  }

  // Get status color
  String get statusColor {
    switch (status) {
      case InventoryStatus.rented:
        return '#FF6B6B'; // Red
      case InventoryStatus.lease:
        return '#4ECDC4'; // Teal
      case InventoryStatus.delivery:
        return '#FFE66D'; // Yellow
    }
  }

  // Get rental period display text
  String get rentalPeriodDisplayText {
    switch (rentalPeriod) {
      case RentalPeriod.day:
        return 'Day';
      case RentalPeriod.month:
        return 'Month';
      case RentalPeriod.quarterly:
        return 'Quarterly';
      case RentalPeriod.year:
        return 'Year';
    }
  }

  // Get product location display text
  String get productLocationDisplayText {
    switch (productLocation) {
      case ProductLocation.inLocker:
        return 'In Locker';
      case ProductLocation.notDelivered:
        return 'Not Delivered';
      case ProductLocation.inTransit:
        return 'In Transit';
    }
  }

  // Get product location color
  String get productLocationColor {
    switch (productLocation) {
      case ProductLocation.inLocker:
        return '#4ECDC4'; // Teal
      case ProductLocation.notDelivered:
        return '#FF6B6B'; // Red
      case ProductLocation.inTransit:
        return '#FFE66D'; // Yellow
    }
  }

  // Calculate refund amount based on remaining days
  double calculateRefund() {
    if (dueDate == null) return 0.0;
    
    final now = DateTime.now();
    final totalDays = dueDate!.difference(createdDate).inDays;
    final remainingDays = dueDate!.difference(now).inDays;
    
    if (remainingDays <= 0) return 0.0;
    
    final refundPercentage = remainingDays / totalDays;
    return price * refundPercentage * 0.8; // 80% refund rate
  }

  // Copy with method for updating properties
  InventoryItem copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    double? price,
    RentalPeriod? rentalPeriod,
    InventoryStatus? status,
    DateTime? createdDate,
    DateTime? dueDate,
    DateTime? deliveryDate,
    String? ownerName,
    String? ownerPhone,
    String? renterName,
    String? renterPhone,
    String? hublockerName,
    String? hublockerAddress,
    double? rating,
    int? reviewCount,
    List<Review>? reviews,
    bool? canCancel,
    String? lockerNumber,
    ProductLocation? productLocation,
    String? qrCode,
    String? accessPin,
    bool? isEditable,
  }) {
    return InventoryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      price: price ?? this.price,
      rentalPeriod: rentalPeriod ?? this.rentalPeriod,
      status: status ?? this.status,
      createdDate: createdDate ?? this.createdDate,
      dueDate: dueDate ?? this.dueDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      ownerName: ownerName ?? this.ownerName,
      ownerPhone: ownerPhone ?? this.ownerPhone,
      renterName: renterName ?? this.renterName,
      renterPhone: renterPhone ?? this.renterPhone,
      hublockerName: hublockerName ?? this.hublockerName,
      hublockerAddress: hublockerAddress ?? this.hublockerAddress,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      reviews: reviews ?? this.reviews,
      canCancel: canCancel ?? this.canCancel,
      lockerNumber: lockerNumber ?? this.lockerNumber,
      productLocation: productLocation ?? this.productLocation,
      qrCode: qrCode ?? this.qrCode,
      accessPin: accessPin ?? this.accessPin,
      isEditable: isEditable ?? this.isEditable,
    );
  }
}

class Review {
  final String id;
  final String userName;
  final String userAvatar;
  final double rating;
  final String comment;
  final DateTime date;

  Review({
    required this.id,
    required this.userName,
    required this.userAvatar,
    required this.rating,
    required this.comment,
    required this.date,
  });
}

class CancelReason {
  final String id;
  final String reason;
  final String? additionalDetails;
  final DateTime cancelDate;
  final double refundAmount;

  CancelReason({
    required this.id,
    required this.reason,
    this.additionalDetails,
    required this.cancelDate,
    required this.refundAmount,
  });
}
