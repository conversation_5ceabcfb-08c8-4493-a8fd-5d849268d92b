enum ProductCategory {
  electronics,
  clothing,
  furniture,
  books,
  sports,
  household,
}

enum DamageLevel {
  none,      // Degree 1 - No damage
  minor,     // Degree 2 - Minor damage
  moderate,  // Degree 3 - Moderate damage
  severe,    // Degree 4 - Severe damage
}

class ProductDamageAssessment {
  final String id;
  final String productId;
  final String productName;
  final ProductCategory category;
  final List<String> beforeImages;
  final List<String> afterImages;
  final double similarityScore;
  final DamageLevel damageLevel;
  final double baseFee;
  final double damageFee;
  final double totalFee;
  final DateTime assessmentDate;
  final String description;
  final List<String> damageDetails;

  ProductDamageAssessment({
    required this.id,
    required this.productId,
    required this.productName,
    required this.category,
    required this.beforeImages,
    required this.afterImages,
    required this.similarityScore,
    required this.damageLevel,
    required this.baseFee,
    required this.damageFee,
    required this.totalFee,
    required this.assessmentDate,
    required this.description,
    this.damageDetails = const [],
  });

  String get damageLevelText {
    switch (damageLevel) {
      case DamageLevel.none:
        return 'No Damage';
      case DamageLevel.minor:
        return 'Minor Damage';
      case DamageLevel.moderate:
        return 'Moderate Damage';
      case DamageLevel.severe:
        return 'Severe Damage';
    }
  }

  int get degreeNumber {
    switch (damageLevel) {
      case DamageLevel.none:
        return 1;
      case DamageLevel.minor:
        return 2;
      case DamageLevel.moderate:
        return 3;
      case DamageLevel.severe:
        return 4;
    }
  }

  String get damageColor {
    switch (damageLevel) {
      case DamageLevel.none:
        return '#4CAF50'; // Green
      case DamageLevel.minor:
        return '#FF9800'; // Orange
      case DamageLevel.moderate:
        return '#FF5722'; // Deep Orange
      case DamageLevel.severe:
        return '#F44336'; // Red
    }
  }

  bool get requiresPayment => damageLevel != DamageLevel.none;
}

class CategoryDamageRates {
  static const Map<ProductCategory, Map<DamageLevel, double>> damageRates = {
    ProductCategory.electronics: {
      DamageLevel.none: 0.0,
      DamageLevel.minor: 0.15,    // 15% of product value
      DamageLevel.moderate: 0.35, // 35% of product value
      DamageLevel.severe: 0.70,   // 70% of product value
    },
    ProductCategory.clothing: {
      DamageLevel.none: 0.0,
      DamageLevel.minor: 0.10,    // 10% of product value
      DamageLevel.moderate: 0.25, // 25% of product value
      DamageLevel.severe: 0.50,   // 50% of product value
    },
    ProductCategory.furniture: {
      DamageLevel.none: 0.0,
      DamageLevel.minor: 0.20,    // 20% of product value
      DamageLevel.moderate: 0.40, // 40% of product value
      DamageLevel.severe: 0.80,   // 80% of product value
    },
    ProductCategory.books: {
      DamageLevel.none: 0.0,
      DamageLevel.minor: 0.05,    // 5% of product value
      DamageLevel.moderate: 0.15, // 15% of product value
      DamageLevel.severe: 0.30,   // 30% of product value
    },
    ProductCategory.sports: {
      DamageLevel.none: 0.0,
      DamageLevel.minor: 0.12,    // 12% of product value
      DamageLevel.moderate: 0.30, // 30% of product value
      DamageLevel.severe: 0.60,   // 60% of product value
    },
    ProductCategory.household: {
      DamageLevel.none: 0.0,
      DamageLevel.minor: 0.08,    // 8% of product value
      DamageLevel.moderate: 0.20, // 20% of product value
      DamageLevel.severe: 0.45,   // 45% of product value
    },
  };

  static double calculateDamageFee(ProductCategory category, DamageLevel level, double productValue) {
    final rate = damageRates[category]?[level] ?? 0.0;
    return productValue * rate;
  }

  static String getCategoryDisplayName(ProductCategory category) {
    switch (category) {
      case ProductCategory.electronics:
        return 'Electronics';
      case ProductCategory.clothing:
        return 'Clothing';
      case ProductCategory.furniture:
        return 'Furniture';
      case ProductCategory.books:
        return 'Books';
      case ProductCategory.sports:
        return 'Sports Equipment';
      case ProductCategory.household:
        return 'Household Items';
    }
  }
}

class AIAnalysisResult {
  final double similarityScore;
  final DamageLevel detectedDamageLevel;
  final List<String> damageDetails;
  final String analysisDescription;
  final DateTime analysisTime;

  AIAnalysisResult({
    required this.similarityScore,
    required this.detectedDamageLevel,
    required this.damageDetails,
    required this.analysisDescription,
    required this.analysisTime,
  });

  // Simulate AI analysis based on similarity score
  static AIAnalysisResult simulateAnalysis(List<String> beforeImages, List<String> afterImages) {
    // Simulate AI processing time
    final random = DateTime.now().millisecondsSinceEpoch % 100;
    
    // Generate similarity score (85-99% for demo)
    final similarityScore = 85.0 + (random % 15);
    
    // Determine damage level based on similarity
    DamageLevel damageLevel;
    List<String> damageDetails = [];
    String description;

    if (similarityScore >= 95) {
      damageLevel = DamageLevel.none;
      description = 'The product is in excellent condition with no visible damage detected.';
    } else if (similarityScore >= 90) {
      damageLevel = DamageLevel.minor;
      damageDetails = ['Minor wear on surface', 'Slight discoloration'];
      description = 'Minor wear detected but product functionality is not affected.';
    } else if (similarityScore >= 80) {
      damageLevel = DamageLevel.moderate;
      damageDetails = ['Visible scratches', 'Some structural wear', 'Color fading'];
      description = 'Moderate damage detected that may affect product appearance.';
    } else {
      damageLevel = DamageLevel.severe;
      damageDetails = ['Significant damage', 'Structural issues', 'Major wear'];
      description = 'Severe damage detected that significantly affects product condition.';
    }

    return AIAnalysisResult(
      similarityScore: similarityScore,
      detectedDamageLevel: damageLevel,
      damageDetails: damageDetails,
      analysisDescription: description,
      analysisTime: DateTime.now(),
    );
  }
}
